"use client";

import { useEffect, useState } from "react";
import { MessageSquare, Plus, Loader2, ChevronRight, Folder, Trash2, Alert<PERSON>ircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { ChatSession } from "@/services/chatbotApi";
import { formatDistanceToNow } from "date-fns";
import { useChatSessionContext } from "@/context/ChatSessionContext";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON><PERSON><PERSON><PERSON>ogH<PERSON><PERSON>,
  AlertDialog<PERSON>itle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { useTranslations } from "next-intl";

export function ChatHistorySidebar() {
  const t = useTranslations();
  const router = useRouter();
  const {
    sessionId,
    sessions,
    loading,
    switchingSession,
    createNewChat,
    switchSession,
    deleteSession
  } = useChatSessionContext();

  const [sessionToDelete, setSessionToDelete] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Group sessions by date
  const groupedSessions = sessions.reduce((groups, session) => {
    const date = new Date(session.timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // Check if the date is today
    if (date.toDateString() === today.toDateString()) {
      groups.today.push(session);
    }
    // Check if the date is yesterday
    else if (date.toDateString() === yesterday.toDateString()) {
      groups.yesterday.push(session);
    }
    // Check if the date is within the last 7 days
    else if (today.getTime() - date.getTime() < 7 * 24 * 60 * 60 * 1000) {
      groups.previousWeek.push(session);
    }
    // Check if the date is within the last 30 days
    else if (today.getTime() - date.getTime() < 30 * 24 * 60 * 60 * 1000) {
      groups.previousMonth.push(session);
    }

    return groups;
  }, {
    today: [] as ChatSession[],
    yesterday: [] as ChatSession[],
    previousWeek: [] as ChatSession[],
    previousMonth: [] as ChatSession[]
  });

  const handleNewChat = () => {
    // Don't do anything if we're already switching sessions
    if (switchingSession) return;

    // Create a new chat session and navigate to its URL
    const newSessionId = createNewChat();
    router.push(`/enterprise/chat/${newSessionId}`);
  };

  const handleSessionSelect = (id: string) => {
    // Don't do anything if we're already switching sessions
    if (switchingSession) return;

    // Navigate to the selected session's URL
    router.push(`/enterprise/chat/${id}`);
  };

  const handleDeleteClick = (e: any, id: string) => {
    e.stopPropagation(); // Prevent triggering the parent click event
    setSessionToDelete(id);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (sessionToDelete) {
      const result = await deleteSession(sessionToDelete);

      if (result.success) {
        toast.success(t('chat-session-deleted-successfully'));

        // If the deleted session was the current one, redirect to the new session
        if (result.newSessionId && sessionToDelete === sessionId) {
          router.push(`/enterprise/chat/${result.newSessionId}`);
        }
      } else {
        toast.error(t('failed-to-delete-chat-session'));
      }

      setIsDeleteDialogOpen(false);
      setSessionToDelete(null);
    }
  };

  return (
    <>
      <SidebarGroup>
        <SidebarGroupLabel>{t('chat')}</SidebarGroupLabel>
        <SidebarMenu className="space-y-1">
          <SidebarMenuItem className="mb-2">
            <SidebarMenuButton
              onClick={handleNewChat}
              tooltip={t('new-chat')}
              className="bg-primary/10 hover:bg-primary/20"
              disabled={switchingSession}
            >
              {switchingSession ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  <span className="text-sm opacity-100 font-medium">Creating...</span>
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  <span className="text-sm opacity-100 font-medium">{t('new-chat')}</span>
                </>
              )}
            </SidebarMenuButton>
          </SidebarMenuItem>

          {loading ? (
            <SidebarMenuItem>
              <SidebarMenuButton disabled>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                <span className="text-sm opacity-100">Loading...</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ) : (
            <div className="space-y-2">
              {/* Today's chats */}
              {groupedSessions.today.length > 0 && (
                <HistoryGroup
                  title={t('today')}
                  icon={Folder}
                  items={groupedSessions.today}
                  currentSessionId={sessionId}
                  onSessionSelect={handleSessionSelect}
                  onDeleteClick={handleDeleteClick}
                />
              )}

              {/* Yesterday's chats */}
              {groupedSessions.yesterday.length > 0 && (
                <HistoryGroup
                  title={t('yesterday')}
                  icon={Folder}
                  items={groupedSessions.yesterday}
                  currentSessionId={sessionId}
                  onSessionSelect={handleSessionSelect}
                  onDeleteClick={handleDeleteClick}
                />
              )}

              {/* Previous week's chats */}
              {groupedSessions.previousWeek.length > 0 && (
                <HistoryGroup
                  title={t('previous-7-days')}
                  icon={Folder}
                  items={groupedSessions.previousWeek}
                  currentSessionId={sessionId}
                  onSessionSelect={handleSessionSelect}
                  onDeleteClick={handleDeleteClick}
                />
              )}

              {/* Previous month's chats */}
              {groupedSessions.previousMonth.length > 0 && (
                <HistoryGroup
                  title={t('previous-30-days')}
                  icon={Folder}
                  items={groupedSessions.previousMonth}
                  currentSessionId={sessionId}
                  onSessionSelect={handleSessionSelect}
                  onDeleteClick={handleDeleteClick}
                />
              )}
            </div>
          )}
        </SidebarMenu>
      </SidebarGroup>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('delete-chat-session')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('are-you-sure-you-want-to-delete-this-chat-session-this-action-cannot-be-undone')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              {t('delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

interface HistoryGroupProps {
  readonly title: string;
  readonly icon: any;
  readonly items: ChatSession[];
  readonly currentSessionId: string;
  readonly onSessionSelect: (sessionId: string) => void;
  readonly onDeleteClick: (e: any, sessionId: string) => void;
}

function HistoryGroup({ title, icon: Icon, items, currentSessionId, onSessionSelect, onDeleteClick }: HistoryGroupProps) {
  const { switchingSession } = useChatSessionContext();
  return (
    <SidebarMenuItem>
      <Collapsible asChild defaultOpen={true} className="group/collapsible">
        <div>
          <CollapsibleTrigger asChild>
            <SidebarMenuButton tooltip={title} className="bg-muted/50 hover:bg-muted">
              <Icon className="mr-2 h-4 w-4" />
              <span className="text-sm font-medium opacity-100">{title}</span>
              <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
            </SidebarMenuButton>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <SidebarMenuSub className="space-y-2 py-2">
              {items.map((session) => (
                <SidebarMenuSubItem key={session.id} className="w-full">
                  <div className="relative group w-full cursor-pointer">
                    <SidebarMenuSubButton
                      onClick={() => onSessionSelect(session.id)}
                      className={session.id === currentSessionId ? "p-5 bg-accent text-accent-foreground border-l-2 border-primary pr-8" : "p-5 pr-8"}
                      disabled={switchingSession}
                    >
                      <MessageSquare className="mr-2 h-4 w-4 flex-shrink-0" />
                      <div className="flex flex-col items-start w-full overflow-hidden">
                        <span className="text-sm font-medium truncate w-full mb-0.5">{session.title}</span>
                        <span className="text-xs text-muted-foreground truncate w-full">
                          {formatDistanceToNow(new Date(session.timestamp), { addSuffix: true })}
                        </span>
                      </div>
                    </SidebarMenuSubButton>
                    <button
                      onClick={(e) => onDeleteClick(e, session.id)}
                      className="absolute right-1.5 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded-sm hover:bg-destructive/10 text-muted-foreground hover:text-destructive z-10"
                      title={t('delete-session')}
                    >
                      <Trash2 className="h-3.5 w-3.5" />
                    </button>
                  </div>
                </SidebarMenuSubItem>
              ))}
            </SidebarMenuSub>
          </CollapsibleContent>
        </div>
      </Collapsible>
    </SidebarMenuItem>
  );
}
