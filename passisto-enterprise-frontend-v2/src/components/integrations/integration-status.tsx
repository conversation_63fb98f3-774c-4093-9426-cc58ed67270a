import { Badge } from "@/components/ui/badge"

interface IntegrationStatusProps {
  status: string
}

export function IntegrationStatus({ status }: IntegrationStatusProps) {
  const statusMap: Record<
    string,
    { label: string; variant: "default" | "secondary" | "destructive" | "outline" | null; customClass?: string }
  > = {
    loading: {
      label: "Loading",
      variant: null,
      customClass: "bg-blue-500 hover:bg-blue-600 text-white",
    },
    completed: {
      label: "Completed",
      variant: null,
      customClass: "bg-green-500 hover:bg-green-600 text-white", // Green for completed
    },
    failed: {
      label: "Failed",
      variant: "destructive",
    },
    refreshing: {
      label: "Refreshing",
      variant: null,
      customClass: "bg-cyan-500 hover:bg-cyan-600 text-white", // Light blue for refreshing
    },
    not_updated: {
      label: "Not Updated",
      variant: null,
      customClass: "bg-yellow-500 hover:bg-yellow-600 text-white", // Yellow for not updated
    },
  }

  const { label, variant, customClass } = statusMap[status] || {
    label: status,
    variant: "outline",
  }

  return (
    <Badge variant={variant} className={customClass || ""}>
      {label}
    </Badge>
  )
}

