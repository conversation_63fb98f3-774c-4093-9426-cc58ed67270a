"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import {
  Plus,
  Search,
  Copy,
  Send,
  Upload,
  FileText,
  Edit,
  Trash2,
  Users,
  CheckCircle,
  Eye,
  Trash,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import {
  GET_ALL_INTERVIEWS_TEMPLATES,
  GET_INTERVIEW_BY_ID,
  SEND_INTERVIEW_TO_CANDIDATS,
} from "@/utils/routes";
import axiosInstance from "@/config/axios";
import { useAuth } from "@clerk/nextjs";
import { checkUsageLimit } from "@/lib/checkUsageLimit";
import { useRouter } from "next/navigation";
import { UsageLimitDialog } from "@/components/usage-limit-dialog";
import { useBackendUser } from "@/hooks/useBackendUser";
import {
  checkPermissions,
  interviewPermissions,
} from "@/utils/ACTION_PERMISSIONS";

interface InterviewTemplate {
  _id: string;
  role: string;
  level: string;
  techstack: string[];
  createdBy: string;
  type: string;
  candidateCount: number;
  completedCount: number;
  questions: string[];
  createdAt: string;
}

export default function InterviewTemplatesPage() {
  const { getToken } = useAuth();
  const router = useRouter();

  const handleCreateInterview = async () => {
    const token = await getToken();
    const result = await checkUsageLimit("interview", token!);

    if (!result.canProceed) {
      setLimitInfo({ type: "interview", limit: result.limit || 0 });
      setShowLimitDialog(true);
      return;
    }
    if (result.canProceed) {
      router.push("/enterprise/ai-agents/ai-interviewer/create");
    }
  };
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [filterLevel, setFilterLevel] = useState("all");
  const [selectedTemplate, setSelectedTemplate] =
    useState<InterviewTemplate | null>(null);
  const [emailList, setEmailList] = useState("");
  const [manualEntries, setManualEntries] = useState([
    { fullName: "", email: "" },
  ]);
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [templates, setTemplates] = useState<InterviewTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showLimitDialog, setShowLimitDialog] = useState(false);
  const [limitInfo, setLimitInfo] = useState({ type: "interview", limit: 0 });
  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const token = await getToken();
      const response = await axiosInstance.get(GET_ALL_INTERVIEWS_TEMPLATES, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const data = await response.data;
      setTemplates(data);
      setLoading(false);
    } catch (error) {
      setError("Error while fetching the templates");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      const permissions = backendUser?.permissions ?? [];

      // Check if user has at least one interview-related permission
      const hasAnyInterviewPermission = checkPermissions(permissions, [
        interviewPermissions.canView,
        interviewPermissions.canCreate,
        interviewPermissions.canUpdate,
        interviewPermissions.canDelete,
        interviewPermissions.canSendToCandidate,
        interviewPermissions.canViewFeedback,
      ]);

      // Redirect if user doesn't have any interview-related permissions
      if (!hasAnyInterviewPermission) {
        toast.error("Permission denied", {
          description:
            "You don't have permission to access the interviews page.",
        });
        router.push("/enterprise/dashboard");
      }
    }
  }, [backendUser, backendUserLoading, router]);

  useEffect(() => {
    fetchTemplates();
  }, []);
  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      template.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.techstack.some((tech) =>
        tech.toLowerCase().includes(searchTerm.toLowerCase())
      );

    const matchesType =
      filterType === "all" ||
      template.type.toLowerCase() === filterType.toLowerCase();
    const matchesLevel =
      filterLevel === "all" ||
      template.level.toLowerCase() === filterLevel.toLowerCase();

    return matchesSearch && matchesType && matchesLevel;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // const handleSendToEmails = async () => {
  //   if (!emailList.trim() || !selectedTemplate) {
  //     toast("Missing Information", {
  //       description: "Please enter at least one email address.",
  //     });
  //     return;
  //   }

  //   const emails = emailList
  //     .split(/[\s,;]+/)
  //     .filter((email) => email.trim() !== "");

  //   console.log(emails);
  //   console.log(selectedTemplate._id);
  //   const response = await axiosInstance.post(SEND_INTERVIEW_TO_CANDIDATS, {
  //     emails: emails,
  //     interviewId: selectedTemplate._id,
  //   });
  //   toast("Interviews Sent", {
  //     description: `Interview template sent to ${emails.length} candidate(s).`,
  //   });

  //   setEmailList("");
  //   setSelectedTemplate(null);
  // };
  const handleSendManualList = async () => {
    if (manualEntries.length === 0) {
      alert("No candidates added.");
      return;
    }
    if (!selectedTemplate) {
      toast("Missing Template", {
        description: "Please select an interview template.",
      });
      return;
    }

    try {
      const token = await getToken();
      const result = await checkUsageLimit("interview", token!);

      if (!result.canProceed) {
        setLimitInfo({ type: "interview", limit: result.limit || 0 });
        setShowLimitDialog(true);
        return;
      }

      const response = await axiosInstance.post(
        SEND_INTERVIEW_TO_CANDIDATS(selectedTemplate?._id),
        { candidates: manualEntries },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      toast("Invitations sent successfully!");
    } catch (error: any) {
      console.error("Error sending candidates:", error);
      const message = error.response?.data || "An unexpected error occurred.";
      alert(message);
    }
  };
  const handleUploadCSV = async () => {
    if (!csvFile || !selectedTemplate) {
      toast("Missing File", {
        description: "Please upload a CSV file with email addresses.",
      });
      return;
    }

    try {
      // Check usage limit first
      const token = await getToken();
      const result = await checkUsageLimit("interview", token!);

      if (!result.canProceed) {
        setLimitInfo({ type: "interview", limit: result.limit || 0 });
        setShowLimitDialog(true);
        return;
      }
      const formData = new FormData();
      formData.append("file", csvFile);

      const response = await axiosInstance.post(
        SEND_INTERVIEW_TO_CANDIDATS(selectedTemplate._id),
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      toast("File Uploaded", {
        description:
          "CSV file processed successfully. Interviews will be sent to the candidates.",
      });

      setCsvFile(null);
      setSelectedTemplate(null);
    } catch (error) {
      toast("Upload Failed", {
        description:
          "There was an error processing the file. Please try again.",
      });
      console.error("Error uploading file:", error);
    }
  };

  const handleDeleteTemplate = async (template: InterviewTemplate) => {
    try {
      const response = await axiosInstance.delete(
        GET_INTERVIEW_BY_ID(template._id)
      );

      if (response.status === 200) {
        await fetchTemplates();
        toast("Template Deleted", {
          description: `"${template.role} ${template.level}" template has been deleted.`,
        });
      } else {
        toast("Error", {
          description: "Failed to delete the template. Please try again.",
        });
      }
    } catch (error) {
      console.error("Delete error:", error);
      toast("Server Error", {
        description: "Something went wrong while deleting the template.",
      });
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">Loading interviews...</p>
        </div>
      </div>
    );
  }
  return (
    <>
      <div className="container mx-auto py-8 px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold">Interview Templates</h1>
            <p className="text-muted-foreground">
              Create and manage interview templates to send to candidates
            </p>
          </div>
          <div className="flex gap-2">
            {interviewPermissions.canCreate(backendUser?.permissions || []) && (
              <Button
                onClick={handleCreateInterview}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" /> New Template
              </Button>
            )}
          </div>
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Filter Templates</CardTitle>
            <CardDescription>
              Find templates by role, type, or level
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="search">Search</Label>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Search by role or tech..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">Type</Label>
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger id="type">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="technical">Technical</SelectItem>
                    <SelectItem value="behavioral">Behavioral</SelectItem>
                    <SelectItem value="system-design">System Design</SelectItem>
                    <SelectItem value="mixed">Mixed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="level">Level</Label>
                <Select value={filterLevel} onValueChange={setFilterLevel}>
                  <SelectTrigger id="level">
                    <SelectValue placeholder="Select level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    <SelectItem value="junior">Junior</SelectItem>
                    <SelectItem value="mid">Mid-level</SelectItem>
                    <SelectItem value="senior">Senior</SelectItem>
                    <SelectItem value="lead">Lead</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredTemplates.length > 0 ? (
            filteredTemplates.map((template) => (
              <Card key={template._id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>{template.role}</CardTitle>
                      <CardDescription>
                        <Badge className="mt-1">{template.level}</Badge>
                        <Badge
                          variant="outline"
                          className="ml-2 mt-1 capitalize"
                        >
                          {template.type}
                        </Badge>
                      </CardDescription>
                    </div>
                    <div className="flex gap-1">
                      <Link href={`interviews/${template._id}`}>
                        {interviewPermissions.canView(
                          backendUser?.permissions || []
                        ) && (
                          <Button variant="ghost" size="icon">
                            <Eye className="h-4 w-4" />
                          </Button>
                        )}
                      </Link>
                      <Link href={`interviews/${template._id}/edit`}>
                        {interviewPermissions.canUpdate(
                          backendUser?.permissions || []
                        ) && (
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                        )}
                      </Link>
                      {interviewPermissions.canDelete(
                        backendUser?.permissions || []
                      ) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteTemplate(template)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="mb-4">
                    <p className="text-sm text-muted-foreground mb-2">
                      Tech Stack:
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {template.techstack.map((tech) => (
                        <Badge key={tech} variant="secondary">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">
                      Questions: {template.questions.length}
                    </p>
                    <ul className="text-sm list-disc pl-5 space-y-1">
                      {template.questions.slice(0, 2).map((question, index) => (
                        <li key={index} className="text-muted-foreground">
                          {question}
                        </li>
                      ))}
                      {template.questions.length > 2 && (
                        <li className="text-muted-foreground">
                          <span className="italic">
                            +{template.questions.length - 2} more questions
                          </span>
                        </li>
                      )}
                    </ul>
                  </div>
                </CardContent>

                <CardFooter className="flex flex-col border-t pt-4 pb-4 gap-2">
                  <div className="flex justify-between w-full items-center">
                    <div className="text-xs text-muted-foreground flex items-center gap-1">
                      <FileText className="h-3 w-3" /> Created by:{" "}
                      {template.createdBy}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Created: {formatDate(template.createdAt)}
                    </div>
                  </div>
                  <div className="flex justify-between w-full">
                    <div className="flex gap-4">
                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                        <Users className="h-3 w-3" /> {template.candidateCount}{" "}
                        candidates
                      </div>
                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                        <CheckCircle className="h-3 w-3" />{" "}
                        {template.completedCount} completed
                      </div>
                    </div>
                    {/* <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          size="sm"
                          onClick={() => setSelectedTemplate(template)}
                        >
                          <Send className="h-3 w-3 mr-1" /> Send
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Send Interview to Candidates</DialogTitle>
                          <DialogDescription>
                            Send the "{template.role} {template.level}" interview
                            to candidates.
                          </DialogDescription>
                        </DialogHeader>
                        <Tabs defaultValue="email-list">
                          <TabsList className="grid w-full grid-cols-1">
                            <TabsTrigger value="csv-upload">
                              CSV Upload
                            </TabsTrigger>
                          </TabsList>
                          <TabsContent
                            value="csv-upload"
                            className="space-y-4 pt-4"
                          >
                            <div className="space-y-2">
                              <Label htmlFor="csv-file">Upload CSV File</Label>
                              <div className="border rounded-md p-4 flex flex-col items-center justify-center gap-2">
                                <Upload className="h-8 w-8 text-muted-foreground" />
                                <p className="text-sm text-muted-foreground">
                                  Drag and drop a CSV file, or click to browse
                                </p>
                                <Input
                                  id="csv-file"
                                  type="file"
                                  accept=".csv"
                                  className="hidden"
                                  onChange={(e) => {
                                    if (
                                      e.target.files &&
                                      e.target.files.length > 0
                                    ) {
                                      setCsvFile(e.target.files[0]);
                                    }
                                  }}
                                />
                                <Button
                                  variant="outline"
                                  onClick={() =>
                                    document.getElementById("csv-file")?.click()
                                  }
                                >
                                  Browse Files
                                </Button>
                                {csvFile && (
                                  <p className="text-sm">
                                    Selected: {csvFile.name}
                                  </p>
                                )}
                              </div>
                              <p className="text-xs text-red-500">
                                CSV file should have 2 columns named “Full Name”
                                and “Email”
                              </p>
                            </div>
                            <DialogFooter>
                              <Button onClick={handleUploadCSV}>
                                Upload and Send
                              </Button>
                            </DialogFooter>
                          </TabsContent>
                        </Tabs>
                      </DialogContent>
                    </Dialog> */}
                    <Dialog>
                      <DialogTrigger asChild>
                        {interviewPermissions.canSendToCandidate(
                          backendUser?.permissions || []
                        ) && (
                          <Button
                            size="sm"
                            onClick={() => setSelectedTemplate(template)}
                          >
                            <Send className="h-3 w-3 mr-1" /> Send
                          </Button>
                        )}
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          
                          <DialogTitle>Invite Candidates

                          </DialogTitle>
                          <DialogDescription>
                            Send the "{selectedTemplate?.role}{" "}
                            {selectedTemplate?.level}" interview to candidates.
                          </DialogDescription>
                        </DialogHeader>

                        <Tabs defaultValue="email-list">
                          <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="email-list">
                              Manual Entry
                            </TabsTrigger>
                            <TabsTrigger value="csv-upload">
                              CSV Upload
                            </TabsTrigger>
                          </TabsList>

                          {/* Manual Entry Tab */}

                          <TabsContent
                            value="email-list"
                            className="space-y-4 pt-4"
                          >
                            <div className="space-y-2">
                              {manualEntries.map((entry, index) => (
                                <div
                                  key={index}
                                  className="flex gap-2 items-center"
                                >
                                  <Input
                                    placeholder="Full Name"
                                    value={entry.fullName}
                                    onChange={(e) => {
                                      const updated = [...manualEntries];
                                      updated[index].fullName = e.target.value;
                                      setManualEntries(updated);
                                    }}
                                  />
                                  <Input
                                    placeholder="Email"
                                    type="email"
                                    value={entry.email}
                                    onChange={(e) => {
                                      const updated = [...manualEntries];
                                      updated[index].email = e.target.value;
                                      setManualEntries(updated);
                                    }}
                                  />
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => {
                                      const updated = manualEntries.filter(
                                        (_, i) => i !== index
                                      );
                                      setManualEntries(updated);
                                    }}
                                  >
                                    <Trash className="h-5 w-5 text-red-500" />
                                  </Button>
                                </div>
                              ))}
                              <Button
                                variant="outline"
                                onClick={() =>
                                  setManualEntries([
                                    ...manualEntries,
                                    { fullName: "", email: "" },
                                  ])
                                }
                              >
                                Add Candidate
                              </Button>
                            </div>

                            <DialogFooter>
                              <Button onClick={handleSendManualList}>
                                Send Invitations
                              </Button>
                            </DialogFooter>
                          </TabsContent>

                          {/* CSV Upload Tab */}
                          <TabsContent
                            value="csv-upload"
                            className="space-y-4 pt-4"
                          >
                            <div className="space-y-2">
                              <Label htmlFor="csv-file">Upload CSV File</Label>
                              <div className="border rounded-md p-4 flex flex-col items-center justify-center gap-2">
                                <Upload className="h-8 w-8 text-muted-foreground" />
                                <p className="text-sm text-muted-foreground">
                                  Drag and drop a CSV file, or click to browse
                                </p>
                                <Input
                                  id="csv-file"
                                  type="file"
                                  accept=".csv"
                                  className="hidden"
                                  onChange={(e) => {
                                    if (
                                      e.target.files &&
                                      e.target.files.length > 0
                                    ) {
                                      setCsvFile(e.target.files[0]);
                                    }
                                  }}
                                />
                                <Button
                                  variant="outline"
                                  onClick={() =>
                                    document.getElementById("csv-file")?.click()
                                  }
                                >
                                  Browse Files
                                </Button>
                                {csvFile && (
                                  <p className="text-sm">
                                    Selected: {csvFile.name}
                                  </p>
                                )}
                              </div>
                              <p className="text-xs text-red-500">
                                CSV file should have 2 columns: "Full Name" and
                                "Email"
                              </p>
                            </div>
                            <DialogFooter>
                              <Button onClick={handleUploadCSV}>
                                Upload and Send
                              </Button>
                            </DialogFooter>
                          </TabsContent>
                        </Tabs>
                      </DialogContent>
                    </Dialog>
                  </div>
                </CardFooter>
              </Card>
            ))
          ) : (
            <div className="md:col-span-2 lg:col-span-3 flex flex-col items-center justify-center py-12">
              <FileText className="h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No templates found</h3>
              <p className="text-muted-foreground mb-4">
                Create your first interview template to get started.
              </p>
              {/* <Link href="/enterprise/ai-agents/ai-interviewer/create">
                <Button>
                  <Plus className="mr-2 h-4 w-4" /> Create Template
                </Button>
              </Link> */}
              {interviewPermissions.canCreate(
                backendUser?.permissions || []
              ) && (
                <Button
                  onClick={handleCreateInterview}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" /> New Template
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
      <UsageLimitDialog
        open={showLimitDialog}
        onOpenChange={setShowLimitDialog}
        type="interview"
        limit={limitInfo.limit}
      />
    </>
  );
}
