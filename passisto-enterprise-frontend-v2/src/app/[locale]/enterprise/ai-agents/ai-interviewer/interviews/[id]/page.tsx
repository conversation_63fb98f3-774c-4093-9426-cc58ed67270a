"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowLeft,
  ChevronDown,
  ChevronUp,
  FileText,
  Plus,
  Search,
  Send,
  Trash,
  Upload,
  Users,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import axiosInstance from "@/config/axios";
import {
  GET_INTERVIEW_BY_ID,
  SEND_INTERVIEW_TO_CANDIDATS,
} from "@/utils/routes";
import InterviewQuestionsList from "../../_components/interview-questions-list";
import { useParams } from "next/navigation";
import { checkUsageLimit } from "@/lib/checkUsageLimit";
import { useAuth } from "@clerk/nextjs";
import { UsageLimitDialog } from "@/components/usage-limit-dialog";
import { useBackendUser } from "@/hooks/useBackendUser";
import {  checkPermissions, interviewPermissions} from "@/utils/ACTION_PERMISSIONS";
import { useRouter } from "next/navigation";
export interface InterviewResponse {
  _id: string;
  role: string;
  type: string;
  level: string;
  techstack: string[];
  questions: string[];
  createdBy: string;
  candidates: Candidate[];
  candidateCount?: number;
  completedCount?: number;
  finalized: boolean;
  createdAt: string;
  company: {
    id: string;
    name: string;
  };
}

export interface Candidate {
  _id: string;
  name: string;
  email: string;
  status: "pending" | "in_progress" | "completed";
  score?: number;
  completedAt?: string;
  hasFeedback: boolean;
  transcript: Message[];
}
export interface Message {
  role: "user" | "assistant";
  content: string;
  timestamp: string;
}
export default function InterviewCandidatesPage() {
  const router = useRouter();
  const { getToken } = useAuth();
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  const params = useParams<{ id: string }>();
  const [interview, setInterview] = useState<InterviewResponse | null>(null);
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [emailList, setEmailList] = useState("");
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [manualEntries, setManualEntries] = useState([
    { fullName: "", email: "" },
  ]);
  const [isDetailsExpanded, setIsDetailsExpanded] = useState(true);
  const [showLimitDialog, setShowLimitDialog] = useState(false);
  const [limitInfo, setLimitInfo] = useState({ type: "interview", limit: 0 });
  const fetchInterview = async (inteviewId: string) => {
    try {
      const response = await axiosInstance.get<InterviewResponse>(
        GET_INTERVIEW_BY_ID(inteviewId)
      );
      console.log(response.data);
      setInterview(response.data);
      setCandidates(response.data.candidates);
      setLoading(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };


  useEffect(() => {
      // Only check permissions after backendUser has loaded
      if (!backendUserLoading && backendUser) {
        const permissions = backendUser?.permissions ?? [];
  
        // Check if user has at least one interview-related permission
        const hasAnyInterviewPermission = checkPermissions(permissions, [
          interviewPermissions.canView,
          
        ]);
  
        // Redirect if user doesn't have any interview-related permissions
        if (!hasAnyInterviewPermission) {
          toast.error("Permission denied", {
            description:
              "You don't have permission to access the interviews page.",
          });
          router.back();
        }
      }
    }, [backendUser, backendUserLoading, router]);

  useEffect(() => {
    if (params.id) {
      fetchInterview(params.id);
    }
  }, [params?.id]);

  // Filter candidates based on search term and status filter
  const filteredCandidates = candidates.filter((candidate) => {
    const matchesSearch =
      candidate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      candidate.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      filterStatus === "all" || candidate.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleSendManualList = async () => {
    if (manualEntries.length === 0) {
      alert("No candidates added.");
      return;
    }

    try {
      // Check usage limit first
      const token = await getToken();
      const result = await checkUsageLimit("interview", token!);
      
      if (!result.canProceed) {
        setLimitInfo({ type: "interview", limit: result.limit || 0 });
        setShowLimitDialog(true);
        return;
      }
      const response = await axiosInstance.post(
        SEND_INTERVIEW_TO_CANDIDATS(params.id),
        { candidates: manualEntries },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      toast("Invitations sent successfully!");
    } catch (error: any) {
      console.error("Error sending candidates:", error);
      const message = error.response?.data || "An unexpected error occurred.";
      alert(message);
    }
  };
  // const handleSendManualList = () => {
  //   if (!emailList.trim()) {
  //     toast("Missing Information", {
  //       description: "Please enter at least one email address.",
  //     });
  //     return;
  //   }

  //   const emails = emailList
  //     .split(/[\s,;]+/)
  //     .filter((email) => email.trim() !== "");

  //   toast("Interviews Sent", {
  //     description: `Interview invitations sent to ${emails.length} candidate(s).`,
  //   });

  //   setEmailList("");
  // };

  const handleUploadCSV = async () => {
    if (!csvFile || !params.id) {
      toast("Missing File", {
        description: "Please upload a CSV file with email addresses.",
      });
      return;
    }

    try {
      // Check usage limit first
      const token = await getToken();
      const result = await checkUsageLimit("interview", token!);
      
      if (!result.canProceed) {
        setLimitInfo({ type: "interview", limit: result.limit || 0 });
        setShowLimitDialog(true);
        return;
      }

      const formData = new FormData();
      formData.append("file", csvFile);
      
      const response = await axiosInstance.post(
        SEND_INTERVIEW_TO_CANDIDATS(params.id),
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      
      toast("File Uploaded", {
        description:
          "CSV file processed successfully. Interviews will be sent to the candidates.",
      });

      setCsvFile(null);
    } catch (error) {
      toast("Upload Failed", {
        description:
          "There was an error processing the file. Please try again.",
      });
      console.error("Error uploading file:", error);
    }
  };
  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">Loading candidates...</p>
        </div>
      </div>
    );
  }

  if (!interview) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card>
          <CardContent className="pt-6 flex flex-col items-center">
            <h2 className="text-xl font-bold mb-2">Interview Not Found</h2>
            <p className="text-center text-muted-foreground mb-6">
              We couldn't find the interview you're looking for. It may have
              been deleted or doesn't exist.
            </p>
            <Link href="/interviews">
              <Button>Return to Interviews</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Link href="/enterprise/ai-agents/ai-interviewer/interviews/">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-2xl font-bold">Interview Candidates</h1>
          </div>
          <p className="text-muted-foreground">
            {interview.role} ({interview.level}) - {interview.type} interview
          </p>
        </div>
        {/* <Dialog>
          <DialogTrigger asChild>
            <Button>
              <Send className="mr-2 h-4 w-4" /> Invite Candidates
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Invite Candidates</DialogTitle>
              <DialogDescription>
                Send the "{interview.role} {interview.level}" interview to
                candidates.
              </DialogDescription>
            </DialogHeader>
            <Tabs defaultValue="email-list">
              <TabsList className="grid w-full grid-cols-1">
                <TabsTrigger value="csv-upload">CSV Upload</TabsTrigger>
              </TabsList>
              <TabsContent value="csv-upload" className="space-y-4 pt-4">
                <div className="space-y-2">
                  <Label htmlFor="csv-file">Upload CSV File</Label>
                  <div className="border rounded-md p-4 flex flex-col items-center justify-center gap-2">
                    <Upload className="h-8 w-8 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">
                      Drag and drop a CSV file, or click to browse
                    </p>
                    <Input
                      id="csv-file"
                      type="file"
                      accept=".csv"
                      className="hidden"
                      onChange={(e) => {
                        if (e.target.files && e.target.files.length > 0) {
                          setCsvFile(e.target.files[0]);
                        }
                      }}
                    />
                    <Button
                      variant="outline"
                      onClick={() =>
                        document.getElementById("csv-file")?.click()
                      }
                    >
                      Browse Files
                    </Button>
                    {csvFile && (
                      <p className="text-sm">Selected: {csvFile.name}</p>
                    )}
                  </div>
                  <p className="text-xs text-red-500">
                    CSV file should have 2 columns named "Full Name" and "Email"
                  </p>
                </div>
                <DialogFooter>
                  <Button onClick={handleUploadCSV}>Upload and Send</Button>
                </DialogFooter>
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog> */}
        <Dialog>
          <DialogTrigger asChild>

            
            <Button>
              <Send className="mr-2 h-4 w-4" /> Invite Candidates
            </Button>
            
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Invite Candidates</DialogTitle>
              <DialogDescription>
                Send the "{interview.role} {interview.level}" interview to
                candidates.
              </DialogDescription>
            </DialogHeader>

            <Tabs defaultValue="email-list">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="email-list">Manual Entry</TabsTrigger>
                <TabsTrigger value="csv-upload">CSV Upload</TabsTrigger>
              </TabsList>

              {/* Manual Entry Tab */}

              <TabsContent value="email-list" className="space-y-4 pt-4">
                <div className="space-y-2">
                  {manualEntries.map((entry, index) => (
                    <div key={index} className="flex gap-2 items-center">
                      <Input
                        placeholder="Full Name"
                        value={entry.fullName}
                        onChange={(e) => {
                          const updated = [...manualEntries];
                          updated[index].fullName = e.target.value;
                          setManualEntries(updated);
                        }}
                      />
                      <Input
                        placeholder="Email"
                        type="email"
                        value={entry.email}
                        onChange={(e) => {
                          const updated = [...manualEntries];
                          updated[index].email = e.target.value;
                          setManualEntries(updated);
                        }}
                      />
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          const updated = manualEntries.filter(
                            (_, i) => i !== index
                          );
                          setManualEntries(updated);
                        }}
                      >
                        <Trash className="h-5 w-5 text-red-500" />
                      </Button>
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    onClick={() =>
                      setManualEntries([
                        ...manualEntries,
                        { fullName: "", email: "" },
                      ])
                    }
                  >
                    Add Candidate
                  </Button>
                </div>

                <DialogFooter>
                  <Button onClick={handleSendManualList}>
                    Send Invitations
                  </Button>
                </DialogFooter>
              </TabsContent>

              {/* CSV Upload Tab */}
              <TabsContent value="csv-upload" className="space-y-4 pt-4">
                <div className="space-y-2">
                  <Label htmlFor="csv-file">Upload CSV File</Label>
                  <div className="border rounded-md p-4 flex flex-col items-center justify-center gap-2">
                    <Upload className="h-8 w-8 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">
                      Drag and drop a CSV file, or click to browse
                    </p>
                    <Input
                      id="csv-file"
                      type="file"
                      accept=".csv"
                      className="hidden"
                      onChange={(e) => {
                        if (e.target.files && e.target.files.length > 0) {
                          setCsvFile(e.target.files[0]);
                        }
                      }}
                    />
                    <Button
                      variant="outline"
                      onClick={() =>
                        document.getElementById("csv-file")?.click()
                      }
                    >
                      Browse Files
                    </Button>
                    {csvFile && (
                      <p className="text-sm">Selected: {csvFile.name}</p>
                    )}
                  </div>
                  <p className="text-xs text-red-500">
                    CSV file should have 2 columns: "Full Name" and "Email"
                  </p>
                </div>
                <DialogFooter>
                  <Button onClick={handleUploadCSV}>Upload and Send</Button>
                </DialogFooter>
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>
      </div>

      {/* Collapsible Interview Details Card */}
      <Card className="mb-8">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div>
            <CardTitle>Interview Details</CardTitle>
            <CardDescription>Information about this interview</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Users className="h-4 w-4" />
              <span>
                {interview.candidateCount || candidates.length} candidates
              </span>
              <span className="mx-1">•</span>
              <span>
                {interview.completedCount ||
                  candidates.filter((c) => c.status === "completed")
                    .length}{" "}
                completed
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsDetailsExpanded(!isDetailsExpanded)}
              className="h-8 w-8 p-0"
            >
              {isDetailsExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
        </CardHeader>
        {isDetailsExpanded && (
          <CardContent>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              <div>
                <p className="text-sm text-muted-foreground mb-1">Role</p>
                <p className="font-medium">{interview.role}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground mb-1">Level</p>
                <p className="font-medium capitalize">{interview.level}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground mb-1">Type</p>
                <p className="font-medium capitalize">{interview.type}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground mb-1">Created By</p>
                <p className="font-medium">{interview.createdBy}</p>
              </div>
              <div className="md:col-span-2 lg:col-span-4">
                <p className="text-sm text-muted-foreground mb-1">Tech Stack</p>
                <div className="flex flex-wrap gap-1">
                  {interview.techstack.map((tech) => (
                    <Badge key={tech} variant="secondary">
                      {tech}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Interview Questions List component */}
      <InterviewQuestionsList questions={interview.questions} />

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Filter Candidates</CardTitle>
          <CardDescription>
            Find candidates by name, email, or status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name or email..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Candidate</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Score</TableHead>
              <TableHead>Completed At</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCandidates.length > 0 ? (
              filteredCandidates.map((candidate) => (
                <TableRow key={candidate._id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{candidate.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {candidate.email}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        candidate.status === "completed"
                          ? "default"
                          : candidate.status === "in_progress"
                          ? "secondary"
                          : "outline"
                      }
                    >
                      {candidate.status === "completed"
                        ? "Completed"
                        : candidate.status === "in_progress"
                        ? "In Progress"
                        : "Pending"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {candidate.score ? (
                      <span
                        className={`font-medium ${
                          candidate.score >= 80
                            ? "text-green-600"
                            : candidate.score >= 60
                            ? "text-yellow-600"
                            : "text-red-600"
                        }`}
                      >
                        {candidate.score}/100
                      </span>
                    ) : (
                      <span className="text-muted-foreground">N/A</span>
                    )}
                  </TableCell>
                  <TableCell>{formatDate(candidate.completedAt)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      {candidate.hasFeedback && (
                        <Link href={`${params.id}/candidates/${candidate._id}`}>
                          <Button variant="ghost" size="sm">
                            <FileText className="h-4 w-4 mr-1" /> View
                          </Button>
                        </Link>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-6">
                  <div className="flex flex-col items-center justify-center">
                    <p className="text-muted-foreground mb-2">
                      No candidates found
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <UsageLimitDialog
      open={showLimitDialog}
      onOpenChange={setShowLimitDialog}
      type="interview"
      limit={limitInfo.limit}
    />
    </div>
  );
}
