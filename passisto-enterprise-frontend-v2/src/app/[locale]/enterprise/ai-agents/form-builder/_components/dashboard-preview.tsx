"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>, Eye, Trash2, <PERSON><PERSON><PERSON> } from "lucide-react"
import type { StoredForm } from "../_lib/types"

export function DashboardPreview() {
  const router = useRouter()
  const [forms, setForms] = useState<StoredForm[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Load forms from localStorage
    const loadForms = () => {
      try {
        const storedForms = localStorage.getItem("forms")
        if (storedForms) {
          setForms(JSON.parse(storedForms))
        }
      } catch (error) {
        console.error("Error loading forms:", error)
      } finally {
        setLoading(false)
      }
    }

    loadForms()

    // Add event listener for storage changes
    window.addEventListener("storage", loadForms)

    return () => {
      window.removeEventListener("storage", loadForms)
    }
  }, [])

  const handleEditForm = (formId: string) => {
    router.push(`/enterprise/ai-agents/form-builder/editor/${formId}`)
  }

  const handleViewForm = (formId: string) => {
    router.push(`public-page/form/${formId}`)
  }

  const handleViewResponses = (formId: string) => {
    router.push(`enterprise/ai-agents/form-builder/responses/${formId}`)
  }

  const handleDeleteForm = (formId: string) => {
    if (confirm("Are you sure you want to delete this form?")) {
      const updatedForms = forms.filter((form) => form.id !== formId)
      localStorage.setItem("forms", JSON.stringify(updatedForms))
      setForms(updatedForms)

      // Trigger storage event for other tabs
      window.dispatchEvent(new Event("storage"))
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }

  if (forms.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Your Forms</CardTitle>
          <CardDescription>
            You haven't created any forms yet. Use the form builder above to get started.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div>
      <h2 className="text-2xl font-bold mb-4">Your Forms</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {forms.map((form) => (
          <Card key={form.id} className="overflow-hidden">
            <CardHeader className="pb-2">
              <CardTitle className="truncate">{form.title}</CardTitle>
              <CardDescription>Created: {new Date(form.createdAt).toLocaleDateString()}</CardDescription>
            </CardHeader>
            <CardContent className="pb-2">
              <p className="text-sm text-gray-500 line-clamp-2">{form.description || "No description"}</p>
              <p className="text-sm mt-1">{form.fields.length} fields</p>
            </CardContent>
            <CardFooter className="flex justify-between pt-2 border-t">
              <div className="flex gap-2">
                <Button size="sm" variant="outline" onClick={() => handleEditForm(form.id)}>
                  <Edit className="h-4 w-4 mr-1" /> Edit
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleViewForm(form.id)}>
                  <Eye className="h-4 w-4 mr-1" /> View
                </Button>
              </div>
              <div className="flex gap-2">
                <Button size="sm" variant="outline" onClick={() => handleViewResponses(form.id)}>
                  <BarChart className="h-4 w-4 mr-1" /> Responses
                </Button>
                <Button size="sm" variant="destructive" onClick={() => handleDeleteForm(form.id)}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}

