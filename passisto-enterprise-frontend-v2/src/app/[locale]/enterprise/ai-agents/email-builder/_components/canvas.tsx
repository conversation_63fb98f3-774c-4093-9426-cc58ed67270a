"use client"

import { useDrop } from "react-dnd"
import type { EmailComponent, ComponentType, ViewMode } from "./email-builder"
import { cn } from "@/lib/utils"
import { DraggableItem } from "./draggable-item"

interface CanvasProps {
  components: EmailComponent[]
  selectedComponent: EmailComponent | null
  setSelectedComponent: (component: EmailComponent | null) => void
  onUpdateComponent: (id: string, updates: Partial<EmailComponent>) => void
  onRemoveComponent: (id: string) => void
  onReorderComponents: (startIndex: number, endIndex: number) => void
  onAddComponent: (type: ComponentType) => void
  viewMode: ViewMode
}

export function Canvas({
  components,
  selectedComponent,
  setSelectedComponent,
  onUpdateComponent,
  onRemoveComponent,
  onReorderComponents,
  onAddComponent,
  viewMode,
}: CanvasProps) {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: "component",
    drop: (item: { type: ComponentType }) => {
      // Add the dragged component to the canvas
      onAddComponent(item.type)
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver({ shallow: true }),
    }),
  }))

  const moveItem = (dragIndex: number, hoverIndex: number) => {
    onReorderComponents(dragIndex, hoverIndex)
  }

  return (
    <div
      ref={drop}
      className={cn("flex flex-col items-center min-h-full p-4 bg-muted/20 overflow-auto", isOver && "bg-muted/40")}
    >
      <div
        className={cn(
          "w-full max-w-[600px] bg-background border shadow-md rounded-md overflow-hidden transition-all",
          viewMode === "tablet" && "max-w-[480px]",
          viewMode === "mobile" && "max-w-[320px]",
        )}
      >
        {components.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-[300px] border-2 border-dashed border-muted-foreground/20 rounded-md p-4 text-center">
            <p className="text-muted-foreground mb-2">Drag components here</p>
            <p className="text-xs text-muted-foreground">or select components from the sidebar</p>
          </div>
        ) : (
          components.map((component, index) => (
            <DraggableItem
              key={component.id}
              index={index}
              component={component}
              isSelected={selectedComponent?.id === component.id}
              onSelect={() => setSelectedComponent(component)}
              onUpdate={(updates) => onUpdateComponent(component.id, updates)}
              onRemove={() => onRemoveComponent(component.id)}
              moveItem={moveItem}
            />
          ))
        )}
      </div>
    </div>
  )
}

