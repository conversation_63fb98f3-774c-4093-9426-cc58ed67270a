"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Mail, Send } from "lucide-react";
import { toast } from "sonner";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuth } from "@clerk/nextjs";
import axiosInstance from "@/config/axios";
import { EMAIL_BUILDER_SEND } from "@/utils/routes";
import { useBackendUser } from "@/hooks/useBackendUser";
import { emailBuilderPermissions} from "@/utils/ACTION_PERMISSIONS";






interface SendEmailDialogProps {
  generateHTML: () => string;
  disabled?: boolean;
}

export function SendEmailDialog({
  generateHTML,
  disabled = false,
}: SendEmailDialogProps) {
  const { getToken } = useAuth()

  const [open, setOpen] = useState(false);
  const [recipientEmail, setRecipientEmail] = useState("");
  const [senderEmail, setSenderEmail] = useState("<EMAIL>");
  const [senderName, setSenderName] = useState("");
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [html, setHtml] = useState<string>("");
  const { backendUser, loading: backendUserLoading } = useBackendUser();

  // Update HTML when dialog opens
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (newOpen) {
      try {
        // Generate fresh HTML when dialog opens
        const freshHTML = generateHTML();
        setHtml(freshHTML);
      } catch (error) {
        console.error("Error generating HTML:", error);
        setHtml("");
        setError("Error generating email HTML. Please try again.");
      }
    }
  };

  const handleSendEmail = async () => {
    if (!recipientEmail || !subject) {
      setError("Recipient email and subject are required");
      return;
    }

    if (!html) {
      setError("Email content could not be generated");
      return;
    }

    setIsSending(true);
    setError(null);

    try {
      // Prepare the data to send to the backend
      const emailData = {
        from: senderEmail || undefined,
        to: recipientEmail,
        subject,
        fromName: senderName || undefined,
        message: message || undefined,
        htmlBody: html,
      };
      const token = await getToken();
console.log(emailData);
      console.log(emailData);
      const response = await axiosInstance.post(EMAIL_BUILDER_SEND,
        emailData,
        {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      // Show success message
      toast("Email sent successfully", {
        description: `Email has been sent to ${recipientEmail}`,
      });

      // Close the dialog
      setOpen(false);

      // Reset form
      setRecipientEmail("");
      // setSenderEmail("");
      setSenderName("");
      setSubject("");
      setMessage("");
    } catch (error) {
      console.error("Error sending email:", error);
      setError(
        error instanceof Error ? error.message : "An unknown error occurred"
      );
      toast("Failed to send email", {
        description: "There was an error sending your email. Please try again.",
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
      {emailBuilderPermissions.canSendBuilderEmail(backendUser?.permissions || []) && (
        <Button variant="outline" className="gap-2" disabled={disabled}>
          <Mail className="h-4 w-4" />
          Send Email
        </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Send Email</DialogTitle>
          <DialogDescription>
            Send your email template to a recipient. Fill in the details below.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="recipient-email" className="text-right">
              To <span className="text-destructive">*</span>
            </Label>
            <Input
              id="recipient-email"
              type="email"
              value={recipientEmail}
              onChange={(e) => setRecipientEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="col-span-3"
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="sender-name" className="text-right">
              From Name
            </Label>
            <Input
              id="sender-name"
              value={senderName}
              onChange={(e) => setSenderName(e.target.value)}
              placeholder="Your Name or Company or Email"
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="sender-email" className="text-right">
              From Email
            </Label>
            <Input
              id="sender-email"
              type="email"
              value={senderEmail}
              // onChange={(e) => setSenderEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="col-span-3"
              disabled
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="subject" className="text-right">
              Subject <span className="text-destructive">*</span>
            </Label>
            <Input
              id="subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder="Email Subject"
              className="col-span-3"
              required
            />
          </div>
          {/* <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="message" className="text-right">
              Message
            </Label>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Optional personal message to include"
              className="col-span-3"
              rows={3}
            />
          </div> */}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          {emailBuilderPermissions.canSendBuilderEmail(backendUser?.permissions || []) && (
          <Button
            onClick={handleSendEmail}
            disabled={isSending}
            className="gap-2"
          >
            {isSending ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Send className="h-4 w-4" />
                Send Email
              </>
            )}
          </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
