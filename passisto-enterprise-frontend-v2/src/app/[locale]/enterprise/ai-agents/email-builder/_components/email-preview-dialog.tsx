"use client"

import { useState } from "react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Eye } from "lucide-react"
import { SendEmailDialog } from "./send-email-dialog"

interface EmailPreviewDialogProps {
  generateHTML: () => string
}

export function EmailPreviewDialog({ generateHTML }: EmailPreviewDialogProps) {
  const [open, setOpen] = useState(false)
  const [html, setHtml] = useState("")

  // Force HTML generation when dialog opens
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen)
    if (newOpen) {
      try {
        // Generate fresh HTML when dialog opens
        const freshHTML = generateHTML()
        setHtml(freshHTML)
      } catch (error) {
        console.error("Error generating HTML:", error)
        setHtml("<p>Error generating preview. Please try again.</p>")
      }
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Eye className="h-4 w-4" />
          Preview & Send
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Email Preview</DialogTitle>
          <DialogDescription>Preview how your email will look to recipients</DialogDescription>
        </DialogHeader>
        <div className="flex-1 overflow-auto border rounded-md my-4">
          <iframe
            srcDoc={html}
            title="Email Preview"
            className="w-full h-full min-h-[400px]"
            sandbox="allow-same-origin"
          />
        </div>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Close
          </Button>
          <SendEmailDialog generateHTML={() => html} />
        </div>
      </DialogContent>
    </Dialog>
  )
}

