"use client"

import { useState } from "react"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Wand2 } from "lucide-react"
import { toast } from "sonner"
import { useTranslations } from "next-intl"

export function AIPromptGenerator() {
  const t  = useTranslations()
  const [userDescription, setUserDescription] = useState("")
  const [activeTab, setActiveTab] = useState<string>("description")

  // Generate the prompt to send to an LLM
  const generatePrompt = () => {
    if (!userDescription.trim()) {
      toast(t('empty-description'),{
        description: t('please-enter-a-description-of-the-email-you-want-to-create'),
      })
      return ""
    }

    // Create the prompt with instructions for the LLM
    /*const prompt = `
You are an email template generator. Create an email template using ONLY the components and settings specified below.

AVAILABLE COMPONENTS:
1. header - A heading for the email
2. text - A paragraph or block of text
3. image - An image with optional link
4. button - A clickable button with link
5. divider - A horizontal line separator
6. footer - Footer text, typically for copyright and unsubscribe info

AVAILABLE SETTINGS FOR EACH COMPONENT:
- All components: padding, margin, alignment (left, center, right), backgroundColor
- Text-based components (header, text, button, footer): fontSize, color, fontWeight, lineHeight
- Image: url, altText, width, height, borderRadius, linkImage (true/false), linkUrl
- Button: url, borderRadius, buttonWidth (auto or full), borderColor, borderWidth
- Divider: dividerColor, dividerWidth, dividerStyle (solid, dashed, dotted)

OUTPUT FORMAT:
Return a JSON array of components. Each component should have an "id", "type", "content" (if applicable), and "settings" object.
Example component:
{
  "id": "unique-id",
  "type": "header",
  "content": "Welcome to Our Newsletter",
  "settings": {
    "padding": "24px",
    "backgroundColor": "#f8fafc",
    "fontSize": "28px",
    "color": "#1e293b",
    "alignment": "center",
    "fontWeight": "bold"
  }
}

USER REQUEST:
${userDescription}

IMPORTANT GUIDELINES:
1. Use ONLY the component types listed above
2. Include appropriate settings for each component
3. For images, use "/placeholder.svg?height=200&width=600" as the URL
4. Make sure the email looks professional and follows good design practices
5. Return ONLY the JSON array, no explanations or other text
`*/
const prompt = `
You are an email template generator. Create an email template using ONLY the components and settings specified below.

AVAILABLE COMPONENTS:
1. header - A heading for the email
2. text - A paragraph or block of text
3. image - An image with optional link
4. button - A clickable button with link
5. divider - A horizontal line separator
6. footer - Footer text, typically for copyright and unsubscribe info

AVAILABLE SETTINGS FOR EACH COMPONENT:
- All components: padding, margin, alignment (left, center, right), backgroundColor
- Text-based components (header, text, button, footer): fontSize, color, fontWeight, lineHeight
- Image: url, altText, width, height, borderRadius, linkImage (true/false), linkUrl
- Button: url, borderRadius, buttonWidth (auto or full), borderColor, borderWidth
- Divider: dividerColor, dividerWidth, dividerStyle (solid, dashed, dotted)

OUTPUT FORMAT:
Return a JSON array of components. Each component should have an "id", "type", "content" (if applicable), and "settings" object.

IMPORTANT GUIDELINES:
1. Use ONLY the component types listed above
2. You are allowed to use components multiple times. For example, you can use multiple images, texts, buttons, dividers, etc.
3. Include appropriate settings for each component
4. For images, use "/placeholder.svg?height=200&width=600" as the URL
5. Make sure the email looks professional and follows good design practices
6. Return ONLY the JSON array, no explanations or other text

USER REQUEST:
${userDescription}


`

    return prompt
  }

  const copyPrompt = () => {
    const prompt = generatePrompt()
    if (prompt) {
      navigator.clipboard.writeText(prompt)
      toast(t('prompt-copied'),{
        description: t('you-can-now-paste-this-prompt-to-your-preferred-ai-model'),
      })
    }
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-primary" />
            {t('ai-email-template-generator')}
          </CardTitle>
          <CardDescription>
            {t('describe-the-email-you-want-to-create-then-use-the-generated-prompt-with-your-preferred-ai-model')}
          </CardDescription>
        </CardHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="description">{t('email-description')}</TabsTrigger>
            <TabsTrigger value="prompt" disabled={!userDescription.trim()}>
              {t('generated-prompt')}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="description" className="mt-0">
            <CardContent className="pt-6">
              <div className="space-y-4">
                <Textarea
                  placeholder={t('describe-the-email-you-want-to-create-for-example-create-a-welcome-email-for-new-customers-of-my-fitness-company-fitlife-with-a-blue-color-scheme')}
                  className="min-h-[200px] resize-none"
                  value={userDescription}
                  onChange={(e) => setUserDescription(e.target.value)}
                />

                <div className="bg-muted/50 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-2">{t('description-tips')}</h3>
                  <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-4">
                    <li>{t('specify-the-type-of-email-welcome-newsletter-promotion-etc')}</li>
                    <li>{t('include-your-company-or-brand-name')}</li>
                    <li>{t('mention-any-color-preferences')}</li>
                    <li>{t('describe-the-tone-formal-casual-exciting')}</li>
                    <li>{t('include-specific-sections-you-want-in-the-email')}</li>
                    <li>{t('mention-any-specific-images-or-buttons-you-want')}</li>
                  </ul>
                </div>
              </div>
            </CardContent>

            <CardFooter>
              <Button onClick={() => setActiveTab("prompt")} disabled={!userDescription.trim()} className="w-full">
                <Wand2 className="mr-2 h-4 w-4" />
                {t('generate-ai-prompt')}
              </Button>
            </CardFooter>
          </TabsContent>

          <TabsContent value="prompt" className="mt-0">
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="relative">
                  <Textarea
                    value={generatePrompt()}
                    readOnly
                    className="min-h-[400px] resize-none font-mono text-xs p-4"
                  />
                  <Button size="sm" variant="secondary" className="absolute top-2 right-2" onClick={copyPrompt}>
                    <Copy className="h-4 w-4 mr-1" />
                    Copy
                  </Button>
                </div>

                <div className="bg-muted/50 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-2">{t('how-to-use-this-prompt')}</h3>
                  <ol className="text-sm text-muted-foreground space-y-1 list-decimal pl-4">
                    <li>{t('copy-the-prompt-above')}</li>
                    <li>{t('paste-it-to-chatgpt-claude-or-your-preferred-ai-model')}</li>
                    <li>{t('the-ai-will-generate-a-json-template-based-on-your-description')}</li>
                    <li>{t('copy-the-json-response')}</li>
                    <li>{t('use-the-import-json-feature-in-our-email-builder-to-load-your-template')}</li>
                  </ol>
                </div>
              </div>
            </CardContent>

            <CardFooter className="flex gap-4">
              <Button variant="outline" onClick={() => setActiveTab("description")} className="flex-1">
                {t('edit-description')}
              </Button>
              <Button onClick={copyPrompt} className="flex-1">
                <Copy className="h-4 w-4 mr-2" />
                {t('copy-prompt')}
              </Button>
            </CardFooter>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  )
}

