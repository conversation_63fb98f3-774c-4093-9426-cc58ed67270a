import type { EmailComponent, ViewMode } from "./email-builder"
import { ComponentPreview } from "./component-preview"
import { cn } from "@/lib/utils"

interface PreviewPanelProps {
  components: EmailComponent[]
  viewMode: ViewMode
}

export function PreviewPanel({ components, viewMode }: PreviewPanelProps) {
  return (
    <div className="flex flex-col items-center min-h-full p-4 bg-muted/20 overflow-auto">
      <div
        className={cn(
          "w-full max-w-[600px] bg-background border shadow-md rounded-md overflow-hidden transition-all",
          viewMode === "tablet" && "max-w-[480px]",
          viewMode === "mobile" && "max-w-[320px]",
        )}
      >
        {components.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-[300px] border-2 border-dashed border-muted-foreground/20 rounded-md p-4 text-center">
            <p className="text-muted-foreground">No components added yet</p>
          </div>
        ) : (
          components.map((component) => <ComponentPreview key={component.id} component={component} isPreview={true} />)
        )}
      </div>
    </div>
  )
}

