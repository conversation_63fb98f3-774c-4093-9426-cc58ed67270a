"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON>, usePara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from "sonner"
import { Shield, ArrowLeft } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { useAppDispatch, useAppSelector } from "@/store/hooks"
import { fetchTeamWithMembers, Permission, updateTeam, CreateTeamDTO } from "@/store/slices/teamSlice"
import { fetchRolesPermissions } from "@/store/slices/rolePermissionSlice"
import { useAuth } from "@clerk/nextjs"
import { Breadcrumb } from "@/components/breadcrumb"
import { teamPermissions } from "@/utils/ACTION_PERMISSIONS";
import { useBackendUser } from "@/hooks/useBackendUser";
import { useTranslations } from "next-intl"

export default function EditTeamPage() {
  const t  = useTranslations()
  const router = useRouter()
  const { id } = useParams<{ id: string }>()
  const dispatch = useAppDispatch()
  const { getToken } = useAuth()
  const { backendUser, loading: backendUserLoading } = useBackendUser();
  
  // Get team from Redux store
  const { currentTeam, status } = useAppSelector((state) => state.teams)
  // Get permissions from rolePermissionSlice
  const { permissionsByCategory, loading: permissionsLoading } = useAppSelector((state) => state.rolePermission)
  
  const [formData, setFormData] = useState<CreateTeamDTO>({
    name: "",
    description: "",
    permissions: []
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formErrors, setFormErrors] = useState<{ 
    name?: string; 
    description?: string; 
    teamExists?: string 
  }>({})

  useEffect(() => {
    // Only check permissions after backendUser has loaded
    if (!backendUserLoading && backendUser) {
      // Redirect if user doesn't have permission to update teams
      if (!teamPermissions.canUpdate(backendUser?.permissions ?? [])) {
        toast.error(t('permission-denied-0'), {
          description: t('you-dont-have-permission-to-edit-teams')
        });
        router.push("/enterprise/teams");
      }
    }
  }, [backendUser, backendUserLoading, router]);

  // Fetch team data and permissions
  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = await getToken()
        
        // Fetch team with members
        await dispatch(fetchTeamWithMembers({ teamId: id as string, token: token! })).unwrap()
        
        // Fetch roles and permissions
        await dispatch(fetchRolesPermissions(token!)).unwrap()
      } catch (error) {
        console.error("Error fetching data:", error)
        toast.error(t('error-loading-data'), {
          description: t('there-was-an-error-loading-the-team-data')
        })
        router.push("/enterprise/teams")
      }
    }

    if (id) {
      fetchData()
    }
  }, [id, dispatch, getToken, router])

  // Update form data when currentTeam changes
  useEffect(() => {
    if (currentTeam) {
      setFormData({
        name: currentTeam.name,
        description: currentTeam.description,
        permissions: currentTeam.permissions?.map(p => p.permissionId || p.action) || []
      })
    }
  }, [currentTeam])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handlePermissionChange = (permissionId: string) => {
    // Check if the permission is already in the team's permissions
    const hasPermission = formData.permissions.includes(permissionId)

    if (hasPermission) {
      // Remove the permission
      setFormData(prev => ({
        ...prev,
        permissions: prev.permissions.filter(p => p !== permissionId)
      }))
    } else {
      // Add the permission
      setFormData(prev => ({
        ...prev,
        permissions: [...prev.permissions, permissionId]
      }))
    }
  }

  // Format permission name for display
  const formatPermissionName = (permission: string) => {
    return permission
      .replace("CAN_", "")
      .split("_")
      .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
      .join(" ")
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!currentTeam) return
    
    setIsSubmitting(true)
    setFormErrors({}) // reset errors

    const errors: { name?: string; description?: string; teamExists?: string } = {};

    // Validation
    if (!formData.name.trim()) {
      errors.name = t('team-name-is-required');
    }

    if (!formData.description.trim()) {
      errors.description = t('team-description-is-required');
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      setIsSubmitting(false);
      return;
    }

    try {
      const token = await getToken()
      
      if (!token) {
        toast.error(t('authentication-required'))
        setIsSubmitting(false)
        return
      }
      
      // Dispatch updateTeam action
      await dispatch(updateTeam({
        teamId: id as string,
        teamData: formData,
        token
      })).unwrap()
      
      toast.success(t('team-updated'), {
        description: `${formData.name} has been updated successfully.`
      })
      
      // Redirect back to team details
      router.push(`/enterprise/teams/${id}/view`)
    } catch (error: any) {
      console.error("Error updating team:", error)
      console.error("Error message:", error.message)
      console.error("Error response:", error.response?.data)
      
      // Check for team already exists error
      if (error.response?.status === 409) {
        setFormErrors({ teamExists: `A team with the name "${formData.name}" already exists.` });
      } else {
        // For debugging, let's set the formErrors state for all errors
        setFormErrors({ teamExists: `A team with the name "${formData.name}" already exists.` });
        
        toast.error(t('update-failed'), {
          description: error.message || t('there-was-an-error-updating-the-team-information')
        })
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  if (status === 'loading' || permissionsLoading) {
    return (
      <div className="container mx-auto py-8 px-4 flex items-center justify-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-muted-foreground">{t('loading-team')}</p>
        </div>
      </div>
    )
  }

  if (!currentTeam) return null

  return (
    <div className="w-full p-4">
      <div className="space-y-2 mb-6">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={() => router.push(`/enterprise/teams/${id}/view`)}
          className="gap-1"
        >
          <ArrowLeft className="h-4 w-4" />
          {t('back-to-team-details')}
        </Button>
        
        <Breadcrumb 
          items={[
            { label: t('teams'), href: "/enterprise/teams" },
            { label: currentTeam.name, href: `/enterprise/teams/${id}/view` },
            { label: t('edit') }
          ]}
          className="ml-1"
        />
      </div>
      
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle>t('edit-team')</CardTitle>
          <CardDescription>{t('update-team-information-and-permissions')}</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4 pt-0">
            {/* Debug info */}
            <div className="hidden">
              {t('form-errors')} {JSON.stringify(formErrors)}
              {t('has-errors')} {Object.keys(formErrors).length > 0 ? t('yes') : t('no')}
            </div>
            
            {/* Alert for form errors */}
            {Object.keys(formErrors).length > 0 && (
              <div className="bg-destructive/15 text-destructive px-4 py-3 rounded-md mb-4 border border-destructive/30">
                <h4 className="font-medium mb-1">
                  {formErrors.teamExists 
                    ? t('the-team-already-exists') 
                    : (formErrors.name || formErrors.description) 
                      ? t('please-fill-in-the-following-fields')
                      : t('error')}
                </h4>
                <ul className="list-disc list-inside text-sm">
                  {formErrors.name && <li>{formErrors.name}</li>}
                  {formErrors.description && <li>{formErrors.description}</li>}
                  {formErrors.teamExists && <li>{formErrors.teamExists}</li>}
                </ul>
              </div>
            )}
            
            <Tabs defaultValue="details">
              <TabsList className="grid w-full grid-cols-2 mb-4">
                <TabsTrigger value="details">{t('team-details')}</TabsTrigger>
                <TabsTrigger value="permissions">{t('team-permissions')}</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-4 pt-4">
                <div className="space-y-2">
                  <Label htmlFor="name">{t('team-name')}</Label>
                  <Input id="name" name="name" value={formData.name} onChange={handleChange}  
                  className={formErrors.name ? "border-red-500" : ""} />
                  
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">{t('description')}</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={4}
                    className={formErrors.description ? "border-red-500" : ""}
                  />
                </div>
              </TabsContent>

              <TabsContent value="permissions" className="pt-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium flex items-center">
                      <Shield className="h-4 w-4 mr-2 text-primary" />
                      {t('team-permissions-2')}
                    </h3>
                    <Badge variant="outline">
                      {formData.permissions?.length || 0} permission{(formData.permissions?.length || 0) !== 1 ? "s" : ""} selected
                    </Badge>
                  </div>

                  <p className="text-sm text-muted-foreground">
                    {t('permissions-assigned-to-this-team-will-be-granted-to-all-team-members-unless-explicitly-revoked-for-specific-users')}
                  </p>

                  {permissionsByCategory.map((category) => (
                    <div key={category.category} className="space-y-2">
                      <h4 className="text-sm font-medium">{category.category}</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 border rounded-md p-3">
                        {category.permissions.map((permission) => (
                          <div key={permission.id || permission.action} className="flex items-center space-x-2">
                            <Checkbox
                              id={`permission-${permission.id || permission.action}`}
                              checked={formData.permissions?.includes(permission.id || permission.action) || false}
                              onCheckedChange={() => handlePermissionChange(permission.id || permission.action)}
                            />
                            <label htmlFor={`permission-${permission.id || permission.action}`} className="text-sm">
                              {formatPermissionName(permission.action)}
                              {permission.scopeType && (
                                <span className="text-xs text-muted-foreground ml-1">
                                  ({permission.scopeType})
                                </span>
                              )}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" onClick={() => router.push(`/enterprise/teams/${id}/view`)}>
              {t('cancel')}
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : t('save-changes')}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}
