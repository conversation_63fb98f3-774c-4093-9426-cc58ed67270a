import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
    reactStrictMode: true,

    // Enable standalone mode only in production environment
    // output: process.env.NODE_ENV === 'production' ? 'standalone' : undefined,
    // output: 'standalone',

    typescript: {
      // Set to false to skip type checking during build
      ignoreBuildErrors: true,
    },

    // swcMinify: true,

    // Configure rewrites to proxy API requests to the backend
    async rewrites() {
      return [
        {
          source: '/api/v1/:path*',
          destination: `${process.env.BACKEND_URL || 'http://localhost:5000'}/api/v1/:path*`,
        },
        {
          source: '/api/workflows/:path*',
          destination: `${process.env.BACKEND_URL || 'http://localhost:5000'}/api/:path*`,
        },
        {
          source: '/socket.io/:path*',
          destination: `${process.env.BACKEND_URL || 'http://localhost:5000'}/socket.io/:path*`,
        },
      ];
    },
  };

  export default withNextIntl(nextConfig);
