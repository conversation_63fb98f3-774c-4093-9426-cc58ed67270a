
services:

  passisto-enterprise-frontend-v2:
    container_name: passisto-enterprise-frontend-v2
    restart: always
    build:
      context: ./passisto-enterprise-frontend-v2
      dockerfile: Dockerfile.mehdi.development
    volumes:
      - ./passisto-enterprise-frontend-v2/src:/app/src:Z
      - ./passisto-enterprise-frontend-v2/messages:/app/messages:Z
    environment:
      - PORT=3030
    ports:
      - 3030:3030
    command: npx next dev 
    networks:
      - pe-net  
  
  passisto-enterprise-backend-v2:
    container_name: passisto-enterprise-backend-v2
    build:
      context: ./passisto-enterprise-backend-v2
      dockerfile: Dockerfile
    env_file:
      - ./passisto-enterprise-backend-v2/.env.mehdi
    environment:
      - PORT=5000
    ports:
      - "5000:5000"
      # - 10.0.4.1:5000:5000
    restart: always
    depends_on:
      - pe-db

    volumes:
      - ./passisto-enterprise-backend-v2/src:/app/src:Z
      - ./passisto-enterprise-backend-v2/prisma:/app/prisma:Z
      # - ./passisto-enterprise-backend-v2/Dockerfile:/app/Dockerfile
    tty: true
    networks: 
      - pe-net
  
  pe-db:
    image: postgres:14-alpine
    container_name: pe-db
    ports:
      - 5932:5432
    restart: always
    volumes:
      - ~/db_tasks_data:/var/lib/postgresql/tasks_data:Z
    environment:
      - POSTGRES_PASSWORD=K5EadzuxNRaIGO0
      - POSTGRES_USER=postgres
      - POSTGRES_DB=passisto
    networks:
      - pe-net

  chatbot:
    container_name: chatbot
    restart: always
    build:
      context: ./passisto-enterprise-chatbot
      dockerfile: Dockerfile
    ports:
      - 5921:5921
      # - 10.0.4.1:5921:5921
    environment:
      - APP_PORT=5921
    env_file:
      - ./passisto-enterprise-chatbot/.env.mehdi
    volumes:
      - ./passisto-enterprise-chatbot/src:/app/src:Z
    depends_on:
      - mongo_db
    networks:
      - pe-net

  mongo_db:
    image: mongo:latest
    container_name: mongo_db
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: root
    volumes:
      - mongo_data:/data/db
    networks:
      - pe-net

  nginx:
    image: nginx:latest
    container_name: nginx
    restart: always
    ports:
      - "80:80"
    volumes:
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf:Z
    networks:
      - pe-net

  opensearch:
    image: opensearchproject/opensearch:2.12.0
    container_name: opensearch
    restart: always
    environment:
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
      - plugins.security.disabled=true
      - node.name=opensearch-node
      - cluster.name=opensearch-cluster
      - OPENSEARCH_INITIAL_ADMIN_PASSWORD=Hasdgg52u1
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - opensearch-data:/usr/share/opensearch/data
    ports:
      - 9200:9200
      - 9600:9600 # for performance analyzer
    networks:
      - pe-net


  rabbitmq-celery:
    image: rabbitmq:3-management
    container_name: rabbitmq-celery
    restart: always
    environment:
      RABBITMQ_DEFAULT_USER: rabbit
      RABBITMQ_DEFAULT_PASS: Fqb3Ita2lsb
    ports:
      - "5672:5672"
      - "15672:15672"
      # - "127.0.0.1:5672:5672"
      # - "127.0.0.1:15672:15672"
      # - "10.0.4.1:5672:5672"
      # - "10.0.4.1:15672:15672"
    networks:
      - pe-net


  redis-celery:
    image: redis:latest
    container_name: redis-celery
    ports:
      - "6278:6379"
      # - "127.0.0.1:6278:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    restart: always
    networks:
      - pe-net


  passisto-flower:
    image: mher/flower
    container_name: passisto-flower
    restart: always

    ports:
      - "5555:5555"
      # - "127.0.0.1:5555:5555"
      # - "10.0.4.1:5555:5555"
    environment:
      - FLOWER_BASIC_AUTH=flower:Yk6AUPVDEg
      - CELERY_BROKER_URL=amqp://rabbit:Fqb3Ita2lsb@rabbitmq-celery:5672
      - CELERY_RESULT_BACKEND=redis://redis-celery:6379
    depends_on:
      - rabbitmq-celery
      - redis-celery
      - passisto-enterprise-backend-v2
    networks:
      - pe-net


  ps-jira-worker:
    build: ./passisto-enterprise-workers/jira-worker
    container_name: ps-jira-worker
    depends_on:
      - passisto-flower
    restart: always

    env_file:
      - ./passisto-enterprise-workers/jira-worker/.env
    command: celery -A tasks worker -l info -Q jira -n jira_worker@%h
    networks:
      - pe-net


  ps-ftp-worker:
    build: ./passisto-enterprise-workers/ftp-worker
    container_name: ps-ftp-worker
    restart: always
    depends_on:
      - passisto-flower
    env_file:
      - ./passisto-enterprise-workers/ftp-worker/.env
    command: celery -A tasks worker -l info -Q ftp -c 1 -n ftp_worker@%h -Ofair
    networks:
      - pe-net


  pe-scrapyd-server:
    build: ./passisto-enterprise-workers/passisto-scrapyd-server
    container_name: scrapyd-server
    restart: always
    depends_on:
      - passisto-flower
      - passisto-enterprise-backend-v2
      - redis-celery
    env_file:
      - ./passisto-enterprise-workers/passisto-scrapyd-server/.env
    ports:
      - 6800:6800
      # - 127.0.0.1:6800:6800
      # - 10.0.4.1:6800:6800
    volumes:
      - ./passisto-enterprise-workers/passisto-scrapyd-server/eggs:/.scrapy/eggs/website_scraper:Z
      - ./passisto-enterprise-workers/passisto-scrapyd-server/logs:/.scrapy/logs/website_scraper/generalspider:Z
    networks:
      - pe-net


  web-worker:
    build: ./passisto-enterprise-workers/web-worker
    container_name: web-worker
    restart: always
    depends_on:
      - pe-scrapyd-server
    env_file:
      - ./passisto-enterprise-workers/passisto-scrapyd-server/.env
    networks:
      - pe-net


networks:
  pe-net:
    external: true

volumes:
  mongo_data:
  pgadmin_data:
  redis-data:
  opensearch-data:

