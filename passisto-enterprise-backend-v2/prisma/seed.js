const prisma = require("../src/config/db");
const roles = ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ME<PERSON><PERSON>", "GUEST"];

const permissionCategories = [
  {
    name: "User & Group Management",
    permissions: [
      "CAN_CREATE_USER",
      "CAN_UPDATE_USER",
      "CAN_DELETE_USER",
      "CAN_VIEW_USER",
      "CAN_CREATE_GROUP",
      "CAN_UPDATE_GROUP",
      "CAN_DELETE_GROUP",
      "CAN_VIEW_GROUP",
      "CAN_ASSIGN_USER_TO_GROUP",
      "CAN_REMOVE_USER_FROM_GROUP",
      "CAN_MANAGE_ROLES",
      "CAN_MANAGE_PERMISSIONS",
      "CAN_INVITE_USER",
    ],
  },
  {
    name: "Data Provider Management",
    permissions: [
      "CAN_CREATE_DATA_PROVIDER_FTP",
      "CAN_CREATE_DATA_PROVIDER_WEBSITE",
      "CAN_CREATE_DATA_PROVIDER_JIRA",
      "CAN_CREATE_DATA_PROVIDER_LOCAL",
      "CAN_UPDATE_DATA_PROVIDER",
      "CAN_DELETE_DATA_PROVIDER",
      "CAN_VIEW_DATA_PROVIDER",
      "CAN_ASSIGN_GROUP_TO_DATA_PROVIDER",
      "CAN_CONFIGURE_DATA_PROVIDER_SETTINGS",
    ],
  },
  {
    name: "Delegation Permissions",
    permissions: [
      "CAN_DELEGATE_CREATE_GROUP",
      "CAN_DELEGATE_CREATE_USER",
      "CAN_DELEGATE_ASSIGN_USER_TO_GROUP",
      "CAN_DELEGATE_CREATE_DATA_PROVIDER_FTP",
      "CAN_DELEGATE_CREATE_DATA_PROVIDER_HTTP",
      "CAN_DELEGATE_CREATE_DATA_PROVIDER_LOCAL",
      "CAN_DELEGATE_MANAGE_ROLES",
      "CAN_DELEGATE_MANAGE_PERMISSIONS",
    ],
  },
  {
    name: "Form Builder",
    permissions: [
      "CAN_CREATE_FORM",
      "CAN_UPDATE_FORM",
      "CAN_DELETE_FORM",
      "CAN_VIEW_FORM",
      "CAN_PUBLISH_FORM",
      "CAN_EXPORT_FORM_DATA",
      "CAN_IMPORT_FORM_TEMPLATE",
    ],
  },
  {
    name: "Email Builder",
    permissions: [
      "CAN_CREATE_EMAIL",
      "CAN_UPDATE_EMAIL",
      "CAN_DELETE_EMAIL",
      "CAN_VIEW_EMAIL",
      "CAN_SEND_EMAIL",
      "CAN_SCHEDULE_EMAIL",
      "CAN_VIEW_EMAIL_ANALYTICS",
      "CAN_EXPORT_EMAIL_TEMPLATES",
    ],
  },
  {
    name: "AI Interview Agent",
    permissions: [
      "CAN_CREATE_INTERVIEW",
      "CAN_UPDATE_INTERVIEW",
      "CAN_DELETE_INTERVIEW",
      "CAN_VIEW_INTERVIEW",
      "CAN_SEND_INTERVIEW_TO_CANDIDATE",
      "CAN_VIEW_INTERVIEW_RESULTS",
      "CAN_EXPORT_INTERVIEW_DATA",
      "CAN_CREATE_FEEDBACK",
      "CAN_VIEW_FEEDBACK",
    ],
  },
  {
    name: "Billing & Subscription",
    permissions: [
      "CAN_VIEW_BILLING_INFO",
      "CAN_UPDATE_BILLING_INFO",
      "CAN_CHANGE_SUBSCRIPTION",
      "CAN_VIEW_USAGE_STATS",
      "CAN_VIEW_INVOICES",
      "CAN_DOWNLOAD_INVOICES",
    ],
  },
  {
    name: "Company Settings",
    permissions: [
      "CAN_VIEW_COMPANY_SETTINGS",
      "CAN_UPDATE_COMPANY_SETTINGS",
      "CAN_MANAGE_COMPANY_BRANDING",
      "CAN_MANAGE_INTEGRATIONS",
      "CAN_VIEW_AUDIT_LOGS",
      "CAN_EXPORT_COMPANY_DATA",
    ],
  },
  {
    name: "API Access",
    permissions: [
      "CAN_CREATE_API_KEY",
      "CAN_REVOKE_API_KEY",
      "CAN_VIEW_API_KEYS",
      "CAN_USE_API",
      "CAN_VIEW_API_LOGS",
    ],
  },
];

async function seed() {
  console.log("Seeding database...");
  try {
    // Create roles
    const roleRecords = await Promise.all(
      roles.map((name) =>
        prisma.role.upsert({
          where: { name },
          update: {},
          create: { name },
        })
      )
    );

    // Create permissions
    const permissionRecords = await Promise.all(
      permissionCategories.flatMap(({ name: category, permissions }) =>
        permissions.map((action) =>
          prisma.permission.upsert({
            where: { action },
            update: { category },
            create: { action, category },
          })
        )
      )
    );

    // // Assign all permissions to ADMIN role
    // const adminRole = roleRecords.find((r) => r.name === "ADMIN");
    // if (adminRole) {
    //   await prisma.role.update({
    //     where: { id: adminRole.id },
    //     data: { permissions: { connect: permissionRecords.map((p) => ({ id: p.id })) } },
    //   });
    // }

    console.log("Seeding completed.");
  } catch (error) {
    console.error("Error seeding database:", error);
  } finally {
    await prisma.$disconnect();
  }
}

seed();