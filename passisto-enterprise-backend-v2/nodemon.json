{"verbose": true, "ignore": ["*.test.js", "*.spec.js"], "watch": ["*.*", "src/index.js", "prisma"], "ext": "js,json", "exec": "node -r dotenv/config src/index.js", "events": {"crash": "echo \"App crashed - waiting for file changes before starting...\"", "restart": "echo \"App restarted due to changes\""}, "env": {"NODE_ENV": "development"}, "delay": "1500", "colours": true, "stdout": true, "stderr": true, "legacyWatch": true}