const { FORM_BUILDER_ENHANCE_DESCRIPTION_PROMPT, FORM_BUILDER_GENERATE_TEMPLATE_PROMPT } = require("../utils/PROMPTS");

const { GoogleGenerativeAI } = require("@google/generative-ai");

const genAI = new GoogleGenerativeAI({
  apiKey: "AIzaSyA-VL6kqfGHXFba8I-JwLe70i--APcXoW4"
});

const GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";
const GEMINI_API_KEY = "AIzaSyA-VL6kqfGHXFba8I-JwLe70i--APcXoW4";


const generateWithAI = async (prompt) => {
  try {
    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        contents: [{ parts: [{ text: prompt }] }],
      }),
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    return data.candidates?.[0]?.content?.parts?.[0]?.text || "";
  } catch (error) {
    console.error("Error calling Gemini API:", error);
    throw error;
  }
};

const  enhanceDescription = async (req, res) => {
  const { userInput } = req.body;

  if (!userInput || !userInput.trim()) {
    return res.status(400).json({ error: "Description is required" });
  }

  try {
    const prompt = FORM_BUILDER_ENHANCE_DESCRIPTION_PROMPT(userInput);
    const enhancedDescription = await generateWithAI(prompt);
    res.json({ enhancedDescription });
  } catch (error) {
    res.status(500).json({ error: "Failed to enhance description" });
  }
};

const  generateForm = async (req, res) => {
  const { userInput } = req.body;

  if (!userInput || !userInput.trim()) {
    return res.status(400).json({ error: "Form intent is required" });
  }

  try {
    const prompt = FORM_BUILDER_GENERATE_TEMPLATE_PROMPT(userInput);
    const text = await generateWithAI(prompt);

    // Extract JSON from response
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const parsedTemplate = JSON.parse(jsonMatch[0]);

      res.json({ formTemplate: parsedTemplate });
    } else {
      res.status(500).json({ error: "Failed to generate a valid form template" });
    }
  } catch (error) {
    res.status(500).json({ error: "Failed to generate form" });
  }
};


module.exports = { enhanceDescription, generateForm };