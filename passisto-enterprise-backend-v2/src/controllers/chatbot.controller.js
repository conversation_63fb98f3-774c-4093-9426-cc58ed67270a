const axios = require("axios");
const { v4: uuidv4 } = require("uuid");
const prisma = require("@/config/db");

// Chatbot API base URL
const CHATBOT_API_BASE_URL = process.env.LLM_API_BASE_URL;


// Send a message to the chatbot
const sendMessage = async (req, res) => {
    const { userId } = req.user;
    const { message, session_id, indices } = req.body;
    try {
        const response = await axios.post(`${CHATBOT_API_BASE_URL}/chatbot`, {
            message,
            indices: indices,
            session_id,
            user_id: userId,
        });
        res.status(200).json(response.data);
    } catch (error) {
        console.error("Error sending message to chatbot:", error.response?.data || error.message);
        res.status(error.response?.status || 500).json({ error: "Failed to send message to chatbot" });
    }
};


// Get chat history for a specific session
const getChatHistory = async (req, res) => {
  const { session_id } = req.params;
  const { user_id } = req.query;
  console.log(session_id)

  try {
    const response = await axios.get(`${CHATBOT_API_BASE_URL}/history/${session_id}`, {
      params: { user_id },
    });

    res.status(200).json(response.data);
  } catch (error) {
    console.error("Error fetching chat history:", error.response?.data || error.message);
    res.status(error.response?.status || 500).json({ error: "Failed to fetch chat history" });
  }
};


// Get all chat sessions for a user
const getUserSessions = async (req, res) => {
  const { userId } = req.user;

  try {
    const response = await axios.get(`${CHATBOT_API_BASE_URL}/user-sessions/${userId}`);

    res.status(200).json(response.data);
  } catch (error) {
    console.error("Error fetching user sessions:", error.response?.data || error.message);
    res.status(error.response?.status || 500).json({ error: "Failed to fetch user sessions" });
  }
};


// Delete a chat session
const deleteSession = async (req, res) => {
  const { session_id } = req.params;
  const { userId } = req.user;

  try {
    const response = await axios.delete(`${CHATBOT_API_BASE_URL}/history/${session_id}`, {
      data: { userId },
    });

    res.status(200).json({ success: true });
  } catch (error) {
    console.error("Error deleting chat session:", error.response?.data || error.message);
    res.status(error.response?.status || 500).json({ error: "Failed to delete chat session" });
  }
};

module.exports = {
  sendMessage,
  getChatHistory,
  getUserSessions,
  deleteSession,
};