const axios = require("axios");
const prisma = require("@/config/db");

// Search API base URL
const SEARCH_API_BASE_URL = process.env.LLM_API_BASE_URL || "https://search-api.example.com";

// Perform a search query
const performSearch = async (req, res) => {
  const { userId } = req.user; // Extract user ID from the authenticated user
  const { query, indices } = req.body;
  
  try {
    const response = await axios.post(`${SEARCH_API_BASE_URL}/search`, {
      query,
      indices: indices,
      user_id: userId,
    });

    res.status(200).json(response.data);
  } catch (error) {
    console.error("Error performing search:", error.response?.data || error.message);
    res.status(error.response?.status || 500).json({ error: "Failed to perform search" });
  }
};

module.exports = {
  performSearch,
};