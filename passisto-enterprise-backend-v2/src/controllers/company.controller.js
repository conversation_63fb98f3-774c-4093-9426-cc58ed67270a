const prisma = require("../config/db");

const checkUsageLimit = async (req, res) => {
  const { companyId } = req.user;
  const { type } = req.body;

  try {
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: true
      }
    });

    if (!company.subscription) {
      return res.status(403).json({
        success: false,
        message: "No active subscription found"
      });
    }

    let isExceeded = false;
    let remaining = 0;

    switch (type) {
      case 'interview':
        isExceeded = company.aiInterviewHoursUsed >= company.subscription.maxAiInterviewHours;
        remaining = company.subscription.maxAiInterviewHours - company.aiInterviewHoursUsed;
        break;
      case 'email':
        isExceeded = company.emailAgentsUsed >= company.subscription.maxEmailAgents;
        remaining = company.subscription.maxEmailAgents - company.emailAgentsUsed;
        break;
      default:
        return res.status(400).json({
          success: false,
          message: "Invalid usage type"
        });
    }

    res.status(200).json({
      success: true,
      isExceeded,
      remaining: parseInt(remaining*60),
      currentUsage: type === 'interview' ?parseInt(company.aiInterviewHoursUsed*60): company.emailAgentsUsed,
      limit: type === 'interview' ? parseInt(company.subscription.maxAiInterviewHours*60) : company.subscription.maxEmailAgents
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to check usage limit"
    });
  }
};

const checkUsageLimitByCompanyId = async (req, res) => {
  const { type, companyId } = req.body;

  try {
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: true
      }
    });

    if (!company.subscription) {
      return res.status(403).json({
        success: false,
        message: "No active subscription found"
      });
    }

    let isExceeded = false;
    let remaining = 0;

    switch (type) {
      case 'interview':
        isExceeded = company.aiInterviewHoursUsed >= company.subscription.maxAiInterviewHours;
        remaining = company.subscription.maxAiInterviewHours - company.aiInterviewHoursUsed;
        break;
      case 'email':
        isExceeded = company.emailAgentsUsed >= company.subscription.maxEmailAgents;
        remaining = company.subscription.maxEmailAgents - company.emailAgentsUsed;
        break;
      default:
        return res.status(400).json({
          success: false,
          message: "Invalid usage type"
        });
    }

    res.status(200).json({
      success: true,
      isExceeded,
      remaining: parseInt(remaining*60),
      currentUsage: type === 'interview' ?parseInt(company.aiInterviewHoursUsed*60): company.emailAgentsUsed,
      limit: type === 'interview' ? parseInt(company.subscription.maxAiInterviewHours*60) : company.subscription.maxEmailAgents
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to check usage limit"
    });
  }
};
const updateCompanyUsage = async (req, res) => {
  const { companyId } = req.user;
  const { type, value } = req.body;

  try {
    const updateData = {};
    
    switch (type) {
      case 'interview':
        updateData.aiInterviewHoursUsed = {
          increment: parseFloat(value)
        };
        break;
      case 'email':
        updateData.emailAgentsUsed = {
          increment: parseInt(value)
        };
        break;
      default:
        return res.status(400).json({
          success: false,
          message: "Invalid update type"
        });
    }

    const updatedCompany = await prisma.company.update({
      where: { id: companyId },
      data: updateData
    });

    res.status(200).json({
      success: true,
      data: updatedCompany
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to update company usage"
    });
  }
};

const getCompanySubscriptionName = async (req, res) => {
  const { companyId } = req.user;

  try {
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: true
      }
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: "Company not found"
      });
    }

    return res.status(200).json({
      success: true,
      subscriptionName: company.subscription?.name || "No Active Subscription"
    });

  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Failed to fetch subscription name"
    });
  }
};

const getCompanyUsageInfo = async (req, res) => {
  const { companyId } = req.user;

  try {
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: true
      }
    });

    if (!company) {
      return res.status(404).json({
        success: false,
        message: "Company not found"
      });
    }

    // If no subscription, return zero limits
    if (!company.subscription) {
      return res.status(200).json({
        success: true,
        emailBuilder: {
          limit: 0,
          used: 0,
          remaining: 0
        },
        aiInterviewer: {
          limit: 0,  // in minutes
          used: 0,   // in minutes
          remaining: 0 // in minutes
        }
      });
    }

    // Convert hours to minutes for AI Interviewer
    const aiInterviewerLimit = Math.round(company.subscription.maxAiInterviewHours * 60);
    const aiInterviewerUsed = Math.round(company.aiInterviewHoursUsed * 60);
    const aiInterviewerRemaining = Math.max(0, aiInterviewerLimit - aiInterviewerUsed);

    // Email Builder calculations
    const emailBuilderLimit = company.subscription.maxEmailAgents;
    const emailBuilderUsed = company.emailAgentsUsed;
    const emailBuilderRemaining = Math.max(0, emailBuilderLimit - emailBuilderUsed);

    return res.status(200).json({
      success: true,
      emailBuilder: {
        limit: emailBuilderLimit,
        used: emailBuilderUsed,
        remaining: emailBuilderRemaining
      },
      aiInterviewer: {
        limit: aiInterviewerLimit,
        used: aiInterviewerUsed,
        remaining: aiInterviewerRemaining
      }
    });

  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Failed to fetch usage information"
    });
  }
};

module.exports = {
  updateCompanyUsage,
  checkUsageLimit,
  getCompanySubscriptionName,
  checkUsageLimitByCompanyId,
  getCompanyUsageInfo
};
