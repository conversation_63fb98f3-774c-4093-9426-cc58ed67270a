const stripe = require("../config/stripe");
const prisma = require("../config/db");

const getPlans = async (req, res) => {
  try {
    const products = await stripe.products.list({
      active: true,
      expand: ["data.default_price"],
    });

    const plans = [];

    for (const product of products.data) {

      // Only include products where is_online is 'false' in metadata
      if (product.metadata.is_online !== "false") {
        continue;
      }

      const prices = await stripe.prices.list({
        product: product.id,
        active: true,
      });

      const maxEmailAgents = product.metadata.maxEmailAgents
        ? parseInt(product.metadata.maxEmailAgents)
        : null;

      // const maxAiInterviewHours = product.metadata.maxAiInterviewHours
      //   ? parseInt(product.metadata.maxAiInterviewHours)
      //   : null;
      const maxAiInterviewMinutes = product.metadata.maxAiInterviewHours
      ? Math.round(parseFloat(product.metadata.maxAiInterviewHours) * 60)
      : null;
      const features = [
        {
          id: "email-builder",
          name: "Email Builder",
          limit: maxEmailAgents !== null ? `${maxEmailAgents} emails/month` : "N/A",
          description: "AI-powered email templates",
        },
        {
          id: "ai-interviewer",
          name: "AI Interviewer",
          limit: maxAiInterviewMinutes !== null ? `${maxAiInterviewMinutes} minutes/month` : "N/A",
          description: "Smart candidate screening",
        },
        {
          id: "priority-support",
          name: "Priority Support",
          limit: "24/7 email support",
          description: "Dedicated assistance",
        },
      ];

      const monthly = prices.data.find(p => p.recurring?.interval === "month");
      const yearly = prices.data.find(p => p.recurring?.interval === "year");

      let annualDiscount = 0;
      if (monthly && yearly) {
        const monthlyFromAnnual = yearly.unit_amount / 12;
        annualDiscount = Math.round((1 - (monthlyFromAnnual / monthly.unit_amount)) * 100);
      }

      plans.push({
        name: product.name,
        monthlyPrice: monthly ? monthly.unit_amount / 100 : 0,
        monthlyPriceId: monthly?.id || null,
        yearlyPriceId: yearly?.id || null,
        annualDiscount: annualDiscount > 0 ? annualDiscount : 0,
        features,
      });
    }

    res.status(200).json(plans.sort((a, b) => a.monthlyPrice - b.monthlyPrice));
  } catch (error) {
    console.error("Error fetching plans:", error);
    res.status(500).json({ error: "Failed to fetch subscription plans" });
  }
};

const createCheckoutSession = async (req, res) => {
  try {
    const { priceId, planName } = req.body;
    const { companyId } = req.user;

    if (!priceId || !companyId) {
      return res.status(400).json({ error: 'Price ID and Company ID are required' });
    }

    // Base session data
    const sessionData = {
      mode: 'subscription',
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      metadata: {
        companyId: companyId,
      },
      success_url: `${process.env.FRONTEND_NGINX_CHECKOUT_SUCESS}?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: process.env.FRONTEND_NGINX_CHECKOUT_CANCEL,
      allow_promotion_codes: true,
    };

    // Add trial-related fields only if plan is 'free'
    if (planName === 'Free') {
      sessionData.payment_method_collection = 'if_required';
      sessionData.subscription_data = {
        trial_period_days: 7,
        trial_settings: {
          end_behavior: {
            missing_payment_method: 'cancel',
          },
        },
      };
    }

    const session = await stripe.checkout.sessions.create(sessionData);

    res.status(200).json({ url: session.url });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    res.status(500).json({ error: 'Failed to create checkout session' });
  }
};

const handleStripeWebhook = async (req, res) => {

  console.log('Received Stripe webhook:', req.body);
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;
  const sig = req.headers["stripe-signature"];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret)
  } catch (err) {
    console.error(`Webhook signature verification failed: ${err.message}`);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object;
        const companyId = session.metadata.companyId;
        
        // Get subscription details
        const subscription = await stripe.subscriptions.retrieve(session.subscription);
        const product = await stripe.products.retrieve(subscription.items.data[0].price.product);

        // Create or update subscription in our database
        const dbSubscription = await prisma.subscription.upsert({
          where: {
            stripeProductId: product.id
          },
          update: {
            name: product.name,
            maxEmailAgents: parseInt(product.metadata.maxEmailAgents || '0'),
            maxAiInterviewHours: parseFloat(product.metadata.maxAiInterviewHours || '0')
          },
          create: {
            name: product.name,
            stripeProductId: product.id,
            maxEmailAgents: parseInt(product.metadata.maxEmailAgents || '0'),
            maxAiInterviewHours: parseFloat(product.metadata.maxAiInterviewHours || '0')
          }
        });

        // Update company with subscription details
        await prisma.company.update({
          where: {
            id: companyId
          },
          data: {
            stripeCustomerId: session.customer,
            stripeSubscriptionId: subscription.id,
            subscriptionStatus: subscription.status,
            subscriptionId: dbSubscription.id
          }
        });
        break;
      }

      case 'customer.subscription.updated': {
        const subscription = event.data.object;
        
        // Get the updated product details
        const product = await stripe.products.retrieve(subscription.items.data[0].price.product);

        // Find company by stripe subscription ID
        const company = await prisma.company.findFirst({
          where: {
            stripeSubscriptionId: subscription.id
          }
        });

        if (company) {
          // Update or create subscription in database
          const dbSubscription = await prisma.subscription.upsert({
            where: {
              stripeProductId: product.id
            },
            update: {
              name: product.name,
              maxEmailAgents: parseInt(product.metadata.maxEmailAgents || '0'),
              maxAiInterviewHours: parseFloat(product.metadata.maxAiInterviewHours || '0')
            },
            create: {
              name: product.name,
              stripeProductId: product.id,
              maxEmailAgents: parseInt(product.metadata.maxEmailAgents || '0'),
              maxAiInterviewHours: parseFloat(product.metadata.maxAiInterviewHours || '0')
            }
          });

          // Update company with new subscription details
          await prisma.company.update({
            where: {
              id: company.id
            },
            data: {
              subscriptionStatus: subscription.status,
              subscriptionId: dbSubscription.id
            }
          });
        }
        break;
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object;
        
        // Find company by stripe subscription ID
        const company = await prisma.company.findFirst({
          where: {
            stripeSubscriptionId: subscription.id
          }
        });

        if (company) {
          await prisma.company.update({
            where: {
              id: company.id
            },
            data: {
              subscriptionStatus: 'canceled',
              subscriptionId: null
            }
          });
        }
        break;
      }
    }

    res.json({ received: true });
  } catch (err) {
    console.error('Error processing webhook:', err);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
};

const checkSubscriptionStatus = async (req, res) => {
  try {
    const { companyId } = req.user;

    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: true
      }
    });

    if (!company) {
      return res.status(404).json({ error: 'Company not found' });
    }

    // If company has no subscription
    if (!company.stripeSubscriptionId) {
      return res.status(200).json({
        status: 'no_subscription',
        subscription: null
      });
    }

    // Get latest subscription details from Stripe
    const stripeSubscription = await stripe.subscriptions.retrieve(company.stripeSubscriptionId);
    console.log(stripeSubscription.status);
    const subscriptionData = {
      status: company.subscriptionStatus,
      stripeStatus: stripeSubscription.status,
      currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
      cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
      limits: {
        maxEmailAgents: company.subscription?.maxEmailAgents || 0,
        maxAiInterviewHours: company.subscription?.maxAiInterviewHours || 0
      }
    };

    // Check if subscription is active
    const isActive = ['active', 'trialing'].includes(stripeSubscription.status);

    // If stripe status differs from our database, update our database
    if (company.subscriptionStatus !== stripeSubscription.status) {
      await prisma.company.update({
        where: { id: companyId },
        data: { subscriptionStatus: stripeSubscription.status }
      });
    }
    console.log(isActive);
    return res.status(200).json({
      active: isActive,
      subscription: subscriptionData
    });

  } catch (error) {
    console.error('Error checking subscription status:', error);
    return res.status(500).json({ error: 'Failed to check subscription status' });
  }
};

const getSubscriptionDetails = async (req, res) => {
  try {
    const { companyId } = req.user;

    const company = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: true
      }
    });

    if (!company) {
      return res.status(404).json({ error: 'Company not found' });
    }

    // If company has no subscription
    if (!company.subscriptionId) {
      return res.status(200).json({
        planName: "No Active Subscription",
        email_builder: {
          limit: "0 emails/month",
          description: "AI-powered email templates"
        },
        ai_interviewer: {
          limit: "0 hours/month",
          description: "AI interview sessions"
        },
        support: {
          limit: "Basic",
          description: "Standard support"
        }
      });
    }

    const subscription = company.subscription;

    // Format the subscription details
    const subscriptionDetails = {
      planName: subscription.name,
      email_builder: {
        limit: `${subscription.maxEmailAgents} emails/month`,
        description: "AI-powered email templates"
      },
      ai_interviewer: {
        limit: `${parseInt(subscription.maxAiInterviewHours*60)} minutes/month`,
        description: "AI interview sessions"
      },
      support: {
        limit: "24/7 Priority",
        description: "Premium support"
      }
    };

    // Add usage information
    const usage = {
      email_builder: {
        used: company.emailAgentsUsed,
        remaining: subscription.maxEmailAgents - company.emailAgentsUsed
      },
      ai_interviewer: {
        used: company.aiInterviewHoursUsed,
        remaining: subscription.maxAiInterviewHours - company.aiInterviewHoursUsed
      }
    };

    return res.status(200).json({
      ...subscriptionDetails,
      usage
    });

  } catch (error) {
    console.error('Error fetching subscription details:', error);
    return res.status(500).json({ error: 'Failed to fetch subscription details' });
  }
};

const createPortalSession = async (req, res) => {
  try {
    const { companyId } = req.user;
    console.log(companyId);
    // Get company details from database
    const company = await prisma.company.findUnique({
      where: { id: companyId }
    });

    if (!company) {
      return res.status(404).json({ error: 'Company not found' });
    }

    if (!company.stripeCustomerId) {
      return res.status(400).json({ error: 'No Stripe customer found for this company' });
    }

    // Create portal session
    const session = await stripe.billingPortal.sessions.create({
      customer: company.stripeCustomerId,
      return_url: `${process.env.FRONTEND_URL}/enterprise/dashboard`,
    });

    res.status(200).json({ url: session.url });
  } catch (error) {
    console.error('Error creating portal session:', error);
    res.status(500).json({ error: 'Failed to create portal session' });
  }
};

module.exports = { 
  getPlans, 
  createCheckoutSession, 
  handleStripeWebhook, 
  checkSubscriptionStatus,
  getSubscriptionDetails,
  createPortalSession 
};
