const prisma = require("../config/db");
const { clerkClient } = require("@clerk/express");
const bcrypt = require("bcryptjs");
const { sendInviteMail } = require("../services/mail.service");

const generateRandomPassword = (length = 12) => {
  const chars =
    "abcdefghijklmnopqrstuvwxyz" +
    "ABCDEFGHIJKLMNOPQRSTUVWXYZ" +
    "0123456789" +
    "!@#$%^&*";
  return Array.from(
    { length },
    () => chars[Math.floor(Math.random() * chars.length)]
  ).join("");
};

const currentUser = async (req, res, next) => {
  try {
    const { userId, companyId } = req.user;
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        company: {
          select: {
            name: true,
          },
        },
        roles: {
          include: {
            permissions: {
              include: { permission: true },
            },
          },
        },
        groups: {
          include: {
            group: {
              include: {
                permissions: {
                  include: { permission: true },
                },
              },
            },
          },
        },
        overrides: {
          include: {
            extraPermissions: { include: { permission: true } },
            revokedPermissions: { include: { permission: true } },
          },
        },
      },
    });

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    const fullName = `${user.firstName} ${user.lastName}`;

    // Extract permissions from roles
    const rolePermissions = user.roles.flatMap((role) =>
      role.permissions.map((rp) => rp.permission.action)
    );

    // Extract permissions from groups
    const groupPermissions = user.groups.flatMap((ug) =>
      ug.group.permissions.map((gp) => gp.permission.action)
    );

    // Extract extra and revoked permissions from overrides
    const extraPermissions =
      user.overrides?.extraPermissions.map((ep) => ep.permission.action) || [];
    const revokedPermissions =
      user.overrides?.revokedPermissions.map((rp) => rp.permission.action) ||
      [];

    // Calculate effective permissions
    const effectivePermissions = [
      ...new Set([
        ...rolePermissions,
        ...groupPermissions,
        ...extraPermissions,
      ]),
    ].filter((permission) => !revokedPermissions.includes(permission));

    return res.status(200).json({
      userId: user.id,
      companyId: user.companyId,
      fullName,
      companyName: user.company.name,
      permissions: effectivePermissions,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ message: "Server error" });
  }
};

const onboarding = async (req, res, next) => {
  try {
    const { userId, companyId } = req.user;
    console.log(userId, companyId);
    const { firstName, lastName, bio, company, role, interests, linkedin } =
      req.body;
    await prisma.company.update({
      where: { id: companyId },
      data: { name: company },
    });
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        firstName,
        lastName,
        bio,
        roleInCompany: role,
        areaOfInterest: interests,
        linkedinProfile: linkedin,
      },
    });

    res.status(200).json({ message: "Onboarding completed" });
  } catch (error) {
    console.error("Onboarding failed:", error);
    return res.status(500).json({ error: "Failed to onboard user" });
  }
};

const createAndInviteUser = async (req, res, next) => {
  try {
    const {
      email,
      firstName,
      lastName,
      groups,
      roles,
      extraPermissions,
      revokedPermissions,
    } = req.body;
    const companyId = req.user.companyId;

    // Validation
    if (
      !email ||
      !firstName ||
      !lastName ||
      !companyId ||
      !Array.isArray(roles)
    ) {
      return res.status(400).json({
        success: false,
        message:
          "Missing required fields. Email, firstName, lastName, and roles are required",
      });
    }

    // Check if user with this email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: "A user with this email already exists",
        userId: existingUser.id,
      });
    }

    // Create or update invitation first
    console.log(
      `Creating invitation for email: ${email} in company ID: ${companyId}`
    );
    const invitation = await prisma.invitation.upsert({
      where: { email },
      update: { companyId },
      create: {
        email,
        companyId,
      },
    });
    console.log(`Invitation created with ID: ${invitation.id}`);

    // Generate and hash password
    const plainPassword = generateRandomPassword();
    const hashedPassword = await bcrypt.hash(plainPassword, 10);

    // Find the Company group
    const companyGroup = await prisma.group.findFirst({
      where: {
        name: "Company",
        companyId: companyId,
      },
    });

    if (!companyGroup) {
      console.log("Company group not found, creating it...");
      // Create the Company group if it doesn't exist
      const newCompanyGroup = await prisma.group.create({
        data: {
          name: "Company",
          description: "Default company-wide group for all employees",
          companyId: companyId,
        },
      });
      console.log(`Created Company group with ID: ${newCompanyGroup.id}`);
    }

    // Prepare groups array, ensuring it's valid
    let userGroups = [];
    if (Array.isArray(groups) && groups.length > 0) {
      // Verify each group exists
      const validGroups = await prisma.group.findMany({
        where: {
          id: { in: groups },
          companyId: companyId,
        },
      });

      userGroups = validGroups.map((g) => ({ id: g.id }));
      console.log(
        `Found ${validGroups.length} valid groups out of ${groups.length} requested`
      );
    }

    // Always add Company group
    if (companyGroup && !userGroups.some((g) => g.id === companyGroup.id)) {
      userGroups.push({ id: companyGroup.id });
    }

    console.log(
      `Creating user in clerk with email: ${email} for company ID: ${companyId}`
    );
    const clerkUser = await clerkClient.users.createUser({
      emailAddress: [email],
      password: plainPassword,
      firstName,
      lastName,
    });

    if (clerkUser) {
      console.log(`Clerk user created successfully with ID: ${clerkUser.id}`);

      // Prepare roles array
      const validRoles = await prisma.role.findMany({
        where: {
          id: { in: roles },
        },
      });

      if (validRoles.length === 0) {
        throw new Error("No valid roles found. Please provide valid role IDs.");
      }

      // Create user without groups first
      const createdUser = await prisma.user.create({
        data: {
          clerkId: clerkUser.id,
          email,
          password: hashedPassword,
          firstName,
          lastName,
          company: {
            connect: { id: companyId },
          },
          roles: {
            connect: validRoles.map((role) => ({ id: role.id })),
          },
        },
      });

      console.log(`User data saved to database with ID: ${createdUser.id}`);

      // Add user to groups separately
      if (userGroups.length > 0) {
        console.log(`Adding user to ${userGroups.length} groups...`);
        for (const group of userGroups) {
          await prisma.userGroup.create({
            data: {
              userId: createdUser.id,
              groupId: group.id,
            },
          });
        }
        console.log(`User added to groups successfully`);
      }

      // Create UserOverride if extra or revoked permissions are provided
      if (
        (extraPermissions && extraPermissions.length > 0) ||
        (revokedPermissions && revokedPermissions.length > 0)
      ) {
        // Create UserOverride record
        const userOverride = await prisma.userOverride.create({
          data: {
            userId: createdUser.id,
          },
        });

        // Process extra permissions
        if (extraPermissions && extraPermissions.length > 0) {
          for (const perm of extraPermissions) {
            await prisma.permissionOverride.create({
              data: {
                permissionId: perm.permissionId,
                scopeType: perm.scopeType || "GLOBAL",
                scopeId: perm.scopeId || "global",
                extraOverrideId: userOverride.id, // Use extraOverrideId instead of extraOverride.connect
              },
            });
          }
          console.log(
            `Added ${extraPermissions.length} extra permissions for user ${createdUser.id}`
          );
        }

        // Process revoked permissions
        if (revokedPermissions && revokedPermissions.length > 0) {
          for (const perm of revokedPermissions) {
            await prisma.permissionOverride.create({
              data: {
                permissionId: perm.permissionId,
                scopeType: perm.scopeType || "GLOBAL",
                scopeId: perm.scopeId || "global",
                revokedOverrideId: userOverride.id, // Use revokedOverrideId instead of revokedOverride.connect
              },
            });
          }
          console.log(
            `Added ${revokedPermissions.length} revoked permissions for user ${createdUser.id}`
          );
        }
      }

      const updatedUser = await clerkClient.users.updateUserMetadata(
        clerkUser.id,
        {
          publicMetadata: {
            companyId: companyId,
            userId: createdUser.id,
          },
        }
      );

      console.log("User metadata updated successfully.", updatedUser);

      // Send invitation email with generated password
      await sendInviteMail(email, plainPassword);

      // Get the complete user with groups, roles, and permission overrides for the response
      const userWithRelations = await prisma.user.findUnique({
        where: { id: createdUser.id },
        include: {
          groups: {
            include: {
              group: true,
            },
          },
          roles: true,
          overrides: {
            include: {
              extraPermissions: {
                include: {
                  permission: true,
                },
              },
              revokedPermissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      });
      console.log(companyId);
      res.status(200).json({
        success: true,
        message: "User created and invited successfully",
        user: {
          id: userWithRelations.id,
          email: userWithRelations.email,
          firstName: userWithRelations.firstName,
          lastName: userWithRelations.lastName,
          groups: userWithRelations.groups.map((ug) => ({
            id: ug.group.id,
            name: ug.group.name,
          })),
          roles: userWithRelations.roles.map((r) => ({
            id: r.id,
            name: r.name,
          })),
          permissions: {
            extraPermissions: userWithRelations.overrides
              ? userWithRelations.overrides.extraPermissions.map((ep) => ({
                  id: ep.id,
                  permissionId: ep.permissionId,
                  action: ep.permission.action,
                  category: ep.permission.category,
                  scopeType: ep.scopeType,
                  scopeId: ep.scopeId,
                }))
              : [],
            revokedPermissions: userWithRelations.overrides
              ? userWithRelations.overrides.revokedPermissions.map((rp) => ({
                  id: rp.id,
                  permissionId: rp.permissionId,
                  action: rp.permission.action,
                  category: rp.permission.category,
                  scopeType: rp.scopeType,
                  scopeId: rp.scopeId,
                }))
              : [],
          },
        },
      });
    }
  } catch (error) {
    console.error("Error occurred while creating and inviting user:", error);
    res.status(500).json({
      success: false,
      message: "Error occurred while creating user",
      error: error.message,
    });
  }
};

const getAllUsers = async (req, res) => {
  try {
    // const { companyId } = req.body;
    const { companyId } = req.user;

    // Get all users with relations
    const users = await prisma.user.findMany({
      where: {
        companyId,
      },
      include: {
        roles: {
          include: {
            permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
        groups: {
          include: {
            group: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
        overrides: {
          include: {
            extraPermissions: {
              include: {
                permission: true,
              },
            },
            revokedPermissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Transform users to include permission counts
    const formattedUsers = users.map((user) => {
      // Get all role permissions
      const rolePermissions = user.roles.flatMap((role) =>
        role.permissions.map(
          (rp) => `${rp.permission.action}:${rp.scopeType}:${rp.scopeId}`
        )
      );

      // Get all group permissions
      const groupPermissions = user.groups.flatMap((ug) =>
        ug.group.permissions.map(
          (gp) => `${gp.permission.action}:${gp.scopeType}:${gp.scopeId}`
        )
      );

      // Get extra permissions
      const extraPermissions =
        user.overrides?.extraPermissions.map(
          (ep) => `${ep.permission.action}:${ep.scopeType}:${ep.scopeId}`
        ) || [];

      // Get revoked permissions
      const revokedPermissions =
        user.overrides?.revokedPermissions.map(
          (rp) => `${rp.permission.action}:${rp.scopeType}:${rp.scopeId}`
        ) || [];

      // Calculate total permissions using the formula:
      // role permissions + group permissions + extra permissions - revoked permissions
      const allPermissions = new Set([
        ...rolePermissions,
        ...groupPermissions,
        ...extraPermissions,
      ]);

      // Remove revoked permissions
      revokedPermissions.forEach((perm) => allPermissions.delete(perm));

      return {
        companyId: user.companyId,
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        name: `${user.firstName} ${user.lastName}`,
        isActive: user.isActive,
        createdAt: user.createdAt,
        roles: user.roles.map((r) => ({
          id: r.id,
          name: r.name,
        })),
        teams: user.groups.map((ug) => ({
          id: ug.group.id,
          name: ug.group.name,
        })),
        permissionsCount: allPermissions.size,
      };
    });

    return res.status(200).json(formattedUsers);
  } catch (error) {
    console.error("Error fetching users:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch users",
      error: error.message,
    });
  }
};

// const getUserById = async (req, res) => {
//   try {
//     const { userId } = req.params;
//     const { companyId } = req.user;

//     // Get user with all relations
//     const user = await prisma.user.findUnique({
//       where: {
//         id: userId,
//         companyId, // Ensure user belongs to the same company
//       },
//       include: {
//         roles: true,
//         groups: {
//           include: {
//             group: true,
//           },
//         },
//         overrides: {
//           include: {
//             extraPermissions: {
//               include: {
//                 permission: true,
//               },
//             },
//             revokedPermissions: {
//               include: {
//                 permission: true,
//               },
//             },
//           },
//         },
//       },
//     });

//     if (!user) {
//       return res.status(404).json({
//         success: false,
//         message: "User not found or does not belong to your company",
//       });
//     }

//     // Format user data for frontend
//     const formattedUser = {
//       id: user.id,
//       email: user.email,
//       firstName: user.firstName,
//       lastName: user.lastName,
//       isActive: user.isActive,
//       createdAt: user.createdAt,
//       permissionsCount: 5,
//       // Format roles
//       roles: user.roles.map((role) => ({
//         id: role.id,
//         name: role.name,
//       })),

//       // Format groups
//       teams: user.groups.map((ug) => ({
//         id: ug.group.id,
//         name: ug.group.name,
//       })),
//       // Format permission overrides
//       extraPermissions: user.overrides
//         ? user.overrides.extraPermissions.map((ep) => ({
//             // id: ep.id,
//             category: ep.permission.category,
//             id: ep.permissionId,
//             action: ep.permission.action,
//             scopeType: ep.scopeType,
//             scopeId: ep.scopeId,
//           }))
//         : [],

//       revokedPermissions: user.overrides
//         ? user.overrides.revokedPermissions.map((rp) => ({
//             // id: rp.id,
//             category: rp.permission.category,
//             id: rp.permissionId,
//             action: rp.permission.action,
//             scopeType: rp.scopeType,
//             scopeId: rp.scopeId,
//           }))
//         : [],
//     };

//     return res.status(200).json(formattedUser);
//   } catch (error) {
//     console.error("Error fetching user by ID:", error);
//     return res.status(500).json({
//       success: false,
//       message: "Failed to fetch user",
//       error: error.message,
//     });
//   }
// };

const getUserById = async (req, res) => {
  try {
    const { userId } = req.params;
    const { companyId } = req.user;

    // Get user with all relations
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
        companyId,
      },
      include: {
        roles: {
          include: {
            permissions: {
              include: {
                permission: true,
              },
            },
          },
        },
        groups: {
          include: {
            group: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
        overrides: {
          include: {
            extraPermissions: {
              include: {
                permission: true,
              },
            },
            revokedPermissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found or does not belong to your company",
      });
    }

    // Extract and merge permissions
    const rolePermissions = user.roles.flatMap((role) =>
      role.permissions.map(
        (rp) => `${rp.permission.action}:${rp.scopeType}:${rp.scopeId}`
      )
    );

    const groupPermissions = user.groups.flatMap((ug) =>
      ug.group.permissions.map(
        (gp) => `${gp.permission.action}:${gp.scopeType}:${gp.scopeId}`
      )
    );

    const extraPermissions =
      user.overrides?.extraPermissions.map(
        (ep) => `${ep.permission.action}:${ep.scopeType}:${ep.scopeId}`
      ) || [];

    const revokedPermissions =
      user.overrides?.revokedPermissions.map(
        (rp) => `${rp.permission.action}:${rp.scopeType}:${rp.scopeId}`
      ) || [];

    const allPermissions = new Set([
      ...rolePermissions,
      ...groupPermissions,
      ...extraPermissions,
    ]);

    revokedPermissions.forEach((perm) => allPermissions.delete(perm));

    const formattedUser = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      isActive: user.isActive,
      createdAt: user.createdAt,
      roles: user.roles.map((role) => ({
        id: role.id,
        name: role.name,
      })),
      teams: user.groups.map((ug) => ({
        id: ug.group.id,
        name: ug.group.name,
      })),
      extraPermissions: user.overrides
        ? user.overrides.extraPermissions.map((ep) => ({
            category: ep.permission.category,
            id: ep.permissionId,
            action: ep.permission.action,
            scopeType: ep.scopeType,
            scopeId: ep.scopeId,
          }))
        : [],
      revokedPermissions: user.overrides
        ? user.overrides.revokedPermissions.map((rp) => ({
            category: rp.permission.category,
            id: rp.permissionId,
            action: rp.permission.action,
            scopeType: rp.scopeType,
            scopeId: rp.scopeId,
          }))
        : [],
      permissionsCount: allPermissions.size,
    };

    return res.status(200).json(formattedUser);
  } catch (error) {
    console.error("Error fetching user by ID:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch user",
      error: error.message,
    });
  }
};

const updateUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const {
      email,
      firstName,
      lastName,
      groups,
      roles,
      extraPermissions,
      revokedPermissions,
      isActive,
    } = req.body;
    const { companyId } = req.user;

    // Validation
    if (
      !userId ||
      !email ||
      !firstName ||
      !lastName ||
      !companyId ||
      !Array.isArray(roles)
    ) {
      return res.status(400).json({
        success: false,
        message:
          "Missing required fields. UserId, email, firstName, lastName, and roles are required",
      });
    }

    // Check if user exists and belongs to the company
    const existingUser = await prisma.user.findUnique({
      where: {
        id: userId,
        companyId,
      },
    });

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        message: "User not found or does not belong to your company",
      });
    }

    // Check if email is being changed and if it's already in use
    if (email !== existingUser.email) {
      const emailInUse = await prisma.user.findUnique({
        where: { email },
      });

      if (emailInUse && emailInUse.id !== userId) {
        return res.status(409).json({
          success: false,
          message: "Email is already in use by another user",
        });
      }
    }

    // Find the Company group
    const companyGroup = await prisma.group.findFirst({
      where: {
        name: "Company",
        companyId,
      },
    });

    // Prepare groups array, ensuring it's valid
    let userGroups = [];
    if (Array.isArray(groups) && groups.length > 0) {
      // Verify each group exists
      const validGroups = await prisma.group.findMany({
        where: {
          id: { in: groups },
          companyId,
        },
      });

      userGroups = validGroups.map((g) => ({ id: g.id }));
      console.log(
        `Found ${validGroups.length} valid groups out of ${groups.length} requested`
      );
    }

    // Always add Company group
    if (companyGroup && !userGroups.some((g) => g.id === companyGroup.id)) {
      userGroups.push({ id: companyGroup.id });
    }

    // Prepare roles array
    const validRoles = await prisma.role.findMany({
      where: {
        id: { in: roles },
      },
    });

    if (validRoles.length === 0) {
      return res.status(400).json({
        success: false,
        message: "No valid roles found. Please provide valid role IDs.",
      });
    }

    // Start a transaction to update everything
    const updatedUser = await prisma.$transaction(async (prisma) => {
      // 1. Update basic user information
      const user = await prisma.user.update({
        where: { id: userId },
        data: {
          email,
          firstName,
          lastName,
          isActive: isActive !== undefined ? isActive : existingUser.isActive,
        },
      });

      // 2. Update roles (remove all and add new ones)
      await prisma.$executeRaw`DELETE FROM "_RoleToUser" WHERE "B" = ${userId}`;

      for (const role of validRoles) {
        await prisma.$executeRaw`INSERT INTO "_RoleToUser" ("A", "B") VALUES (${role.id}, ${userId})`;
      }

      // 3. Update groups (remove all and add new ones)
      await prisma.userGroup.deleteMany({
        where: { userId },
      });

      for (const group of userGroups) {
        await prisma.userGroup.create({
          data: {
            userId,
            groupId: group.id,
          },
        });
      }

      // 4. Update permission overrides
      // First, check if user has overrides
      let userOverride = await prisma.userOverride.findUnique({
        where: { userId },
      });

      // If no overrides exist but we need them, create the record
      if (
        !userOverride &&
        ((extraPermissions && extraPermissions.length > 0) ||
          (revokedPermissions && revokedPermissions.length > 0))
      ) {
        userOverride = await prisma.userOverride.create({
          data: { userId },
        });
      }

      // If we have overrides to process
      if (userOverride) {
        // Delete existing permission overrides
        await prisma.permissionOverride.deleteMany({
          where: {
            OR: [
              { extraOverrideId: userOverride.id },
              { revokedOverrideId: userOverride.id },
            ],
          },
        });

        // Add new extra permissions
        if (extraPermissions && extraPermissions.length > 0) {
          for (const perm of extraPermissions) {
            await prisma.permissionOverride.create({
              data: {
                permissionId: perm.permissionId,
                scopeType: perm.scopeType || "GLOBAL",
                scopeId: perm.scopeId || "global",
                extraOverrideId: userOverride.id,
              },
            });
          }
        }

        // Add new revoked permissions
        if (revokedPermissions && revokedPermissions.length > 0) {
          for (const perm of revokedPermissions) {
            await prisma.permissionOverride.create({
              data: {
                permissionId: perm.permissionId,
                scopeType: perm.scopeType || "GLOBAL",
                scopeId: perm.scopeId || "global",
                revokedOverrideId: userOverride.id,
              },
            });
          }
        }
      }

      return user;
    });

    // Get the updated user with all relations for the response
    const userWithRelations = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        groups: {
          include: {
            group: true,
          },
        },
        roles: true,
        overrides: {
          include: {
            extraPermissions: {
              include: {
                permission: true,
              },
            },
            revokedPermissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    });

    // Format the response
    const formattedUser = {
      id: userWithRelations.id,
      email: userWithRelations.email,
      firstName: userWithRelations.firstName,
      lastName: userWithRelations.lastName,
      isActive: userWithRelations.isActive,
      groups: userWithRelations.groups.map((ug) => ({
        id: ug.group.id,
        name: ug.group.name,
      })),
      roles: userWithRelations.roles.map((r) => ({
        id: r.id,
        name: r.name,
      })),
      permissions: {
        extraPermissions: userWithRelations.overrides
          ? userWithRelations.overrides.extraPermissions.map((ep) => ({
              id: ep.id,
              permissionId: ep.permissionId,
              action: ep.permission.action,
              category: ep.permission.category,
              scopeType: ep.scopeType,
              scopeId: ep.scopeId,
            }))
          : [],
        revokedPermissions: userWithRelations.overrides
          ? userWithRelations.overrides.revokedPermissions.map((rp) => ({
              id: rp.id,
              permissionId: rp.permissionId,
              action: rp.permission.action,
              category: rp.permission.category,
              scopeType: rp.scopeType,
              scopeId: rp.scopeId,
            }))
          : [],
      },
    };

    res.status(200).json({
      success: true,
      message: "User updated successfully",
      user: formattedUser,
    });
  } catch (error) {
    console.error("Error updating user:", error);
    res.status(500).json({
      success: false,
      message: "Error occurred while updating user",
      error: error.message,
    });
  }
};
/**
 * Toggle user active status (and ban/unban in Clerk)
 */
const toggleUserStatus = async (req, res) => {
  try {
    const { userId } = req.params;
    const { companyId } = req.user;

    // Check if user exists and belongs to the company
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
        companyId,
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found or does not belong to your company",
      });
    }

    // Toggle isActive status
    const newStatus = !user.isActive;

    // Update user in our database
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { isActive: newStatus },
    });

    // Update user in Clerk
    if (user.clerkId) {
      try {
        await clerkClient.users.updateUser(user.clerkId, {
          banned: !newStatus, // banned is the opposite of isActive
        });
        console.log(
          `User ${userId} (Clerk ID: ${user.clerkId}) has been ${
            newStatus ? "unbanned" : "banned"
          } in Clerk`
        );
      } catch (clerkError) {
        console.error(
          `Error updating user ban status in Clerk: ${clerkError.message}`
        );
        // Continue with the response even if Clerk update fails
      }
    }

    return res.status(200).json({
      success: true,
      message: `User has been ${
        newStatus ? "enabled" : "disabled"
      } successfully`,
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        isActive: updatedUser.isActive,
      },
    });
  } catch (error) {
    console.error("Error toggling user status:", error);
    return res.status(500).json({
      success: false,
      message: "Error occurred while toggling user status",
      error: error.message,
    });
  }
};

/**
 * Delete a user from both database and Clerk
 */
const deleteUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const { companyId } = req.user;

    // Check if user exists and belongs to the company
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
        companyId,
      },
      include: {
        overrides: true,
        groups: true,
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found or does not belong to your company",
      });
    }

    // Store clerk ID for later use
    const clerkId = user.clerkId;

    // Use a transaction to ensure all database operations succeed or fail together
    await prisma.$transaction(async (prisma) => {
      // Step 1: Delete user's permission overrides if they exist
      if (user.overrides) {
        console.log(`Deleting permission overrides for user ${user.id}`);

        // Delete extra permissions
        await prisma.permissionOverride.deleteMany({
          where: { extraOverrideId: user.overrides.id },
        });

        // Delete revoked permissions
        await prisma.permissionOverride.deleteMany({
          where: { revokedOverrideId: user.overrides.id },
        });

        // Delete the user override record
        await prisma.userOverride.delete({
          where: { id: user.overrides.id },
        });
      }

      // Step 2: Delete user's group memberships
      await prisma.userGroup.deleteMany({
        where: { userId: user.id },
      });

      // Step 3: Delete user's role associations
      await prisma.$executeRaw`DELETE FROM "_RoleToUser" WHERE "B" = ${user.id}`;

      // Step 4: Delete the user
      await prisma.user.delete({
        where: { id: user.id },
      });
    });

    // Delete user from Clerk
    if (clerkId) {
      try {
        await clerkClient.users.deleteUser(clerkId);
        console.log(`User with Clerk ID ${clerkId} deleted from Clerk`);
      } catch (clerkError) {
        console.error(`Error deleting user from Clerk: ${clerkError.message}`);
        // Continue with the response even if Clerk deletion fails
        // We've already deleted from our database
      }
    }

    return res.status(200).json({
      success: true,
      message: "User has been deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting user:", error);
    return res.status(500).json({
      success: false,
      message: "Error occurred while deleting user",
      error: error.message,
    });
  }
};

/**
 * Test API to get a list of users with specific fields
 */
const testGetUsers = async (req, res) => {
  try {
    const { companyId } = req.params;

    // Get all users for the company
    const users = await prisma.user.findMany({
      where: {
        companyId,
      },
      select: {
        id: true,
        clerkId: true,
        firstName: true,
        lastName: true,
        companyId: true,
      },
    });

    // For each user, fetch their Clerk metadata
    const usersWithMetadata = await Promise.all(
      users.map(async (user) => {
        try {
          // Only fetch from Clerk if we have a clerkId
          if (user.clerkId) {
            const clerkUser = await clerkClient.users.getUser(user.clerkId);

            return {
              id: user.id,
              name: `${user.firstName} ${user.lastName}`,
              userId: user.id,
              clerkUserId: user.clerkId,
              companyId: user.companyId,
              clerkPublicMetadata: clerkUser.publicMetadata || {},
            };
          } else {
            return {
              id: user.id,
              name: `${user.firstName} ${user.lastName}`,
              userId: user.id,
              clerkUserId: user.clerkId,
              companyId: user.companyId,
              clerkPublicMetadata: {},
            };
          }
        } catch (error) {
          console.error(
            `Error fetching Clerk data for user ${user.id}:`,
            error
          );
          // Return user without Clerk metadata if there was an error
          return {
            id: user.id,
            name: `${user.firstName} ${user.lastName}`,
            userId: user.id,
            clerkUserId: user.clerkId,
            companyId: user.companyId,
            clerkPublicMetadata: { error: "Failed to fetch Clerk metadata" },
          };
        }
      })
    );

    return res.status(200).json(usersWithMetadata);
  } catch (error) {
    console.error("Error in test API:", error);
    return res.status(500).json({
      success: false,
      message: "Error fetching test user data",
      error: error.message,
    });
  }
};

module.exports = {
  onboarding,
  currentUser,
  createAndInviteUser,
  getAllUsers,
  getUserById,
  updateUser,
  toggleUserStatus,
  deleteUser,
  testGetUsers,
};
