const prisma = require("../config/db");
const bcrypt = require("bcrypt");
const { v4: uuidv4 } = require("uuid");
const { clerkClient } = require("@clerk/express");
const { faker } = require("@faker-js/faker");
const { seedFakeIntegrations } = require("./integration.seeder");

// Sample company data - just one company
const companyTemplate = {
  name: "Passisto",
  description: "Enterprise AI Solutions",
};

// Define roles
const roles = ["ADMIN", "MANAGER", "MEMBER", "GUEST", "PUBLIC"];

// Define permission categories
const permissionCategories = [
  {
    name: "User & Group Management",
    permissions: [
      "CAN_MANAGE_USERS",
      "CAN_CREATE_USER",
      "CAN_UPDATE_USER",
      "CAN_DELETE_USER",
      "CAN_VIEW_USER",
      "CAN_TOGGLE_USER_STATUS",
      "CAN_MANAGE_GROUPS",
      "CAN_CREATE_GROUP",
      "CAN_UPDATE_GROUP",
      "CAN_DELETE_GROUP",
      "CAN_VIEW_GROUP",
      "CAN_ASSIGN_USER_TO_GROUP",
      "CAN_REMOVE_USER_FROM_GROUP",
    ],
  },
  {
    name: "Form Builder",
    permissions: [
      "CAN_CREATE_FORM",
      "CAN_UPDATE_FORM",
      "CAN_DELETE_FORM",
      "CAN_VIEW_FORM",
      "CAN_PUBLISH_FORM",
      "CAN_EXPORT_FORM_DATA",
      "CAN_IMPORT_FORM_TEMPLATE",
    ],
  },
  {
    name: "Data Provider Management",
    permissions: [
      // ALL 
      "CAN_MANAGE_INTEGRATIONS",

      // General
      "CAN_VIEW_INTEGRATION",
      "CAN_ASSIGN_INTEGRATION_TO_GROUP",
      "CAN_REMOVE_INTEGRATION_FROM_GROUP",

      // FTP
      "CAN_CREATE_FTP",
      "CAN_UPDATE_FTP",
      "CAN_DELETE_FTP",

      // Jira
      "CAN_CREATE_JIRA",
      "CAN_UPDATE_JIRA",
      "CAN_DELETE_JIRA",

      // Web
      "CAN_CREATE_WEB",
      "CAN_UPDATE_WEB",
      "CAN_DELETE_WEB",
    ],
  },
  {
    name: "Email Builder",
    permissions: [
      "CAN_ENHANCE_EMAIL_BUILDER_DESCRIPTION",
      "CAN_GENERATE_EMAIL_BUILDER_TEMPLATE",
      "CAN_VIEW_EMAIL_BUILDER_TEMPLATE",
      "CAN_EDIT_EMAIL_BUILDER",
      "CAN_DELETE_EMAIL_BUILDER",
      "CAN_SEND_EMAIL_BUILDER",
      "CAN_MANAGE_EMAIL_BUILDER",
    ],
  },
  {
    name: "AI Interview Agent",
    permissions: [
      "CAN_CREATE_INTERVIEW",
      "CAN_UPDATE_INTERVIEW",
      "CAN_DELETE_INTERVIEW",
      "CAN_VIEW_INTERVIEW",
      "CAN_SEND_INTERVIEW_TO_CANDIDATE",
      // "CAN_VIEW_INTERVIEW_RESULTS",
      // "CAN_EXPORT_INTERVIEW_DATA",
      // "CAN_CREATE_FEEDBACK",
      "CAN_VIEW_FEEDBACK",
      "CAN_MANAGE_INTERVIEW_AGENT",
    ],
  },
  {
    name: "Billing & Subscription",
    permissions: [
      "CAN_VIEW_PLANS",
      "CAN_VIEW_SUBSCRIPTION_STATUS",
      "CAN_VIEW_SUBSCRIPTION_DETAILS",
      "CAN_CREATE_CHECKOUT_SESSION",
      "CAN_CREATE_PORTAL_SESSION",
      // "CAN_MANAGE_BILLING",
    ],
    
  },
  {    
    name: "Chatbot & Search",
    permissions: [
      "CAN_ASK_CHATBOT",
      "CAN_SEARCH"
    ],  
  },
  {
    name: "Chatbot & Search",
    permissions: ["CAN_ASK_CHATBOT", "CAN_SEARCH"],
  },
];

// Define standard groups that will be created for each company
const standardGroups = [
  {
    name: "Company",
    description: "Default company-wide group for all employees",
  },
  {
    name: "Engineering",
    description: "Engineering and Development team",
  },
  {
    name: "Marketing",
    description: "Marketing and Communications team",
  },
  {
    name: "Sales",
    description: "Sales and Business Development team",
  },
  {
    name: "Finance",
    description: "Finance and Accounting department",
  },
];

// Generate 20 users with different roles using Faker
const generateUsers = () => {
  const users = [];

  // Admin user (1)
  // users.push({
  //   firstName: "Abdelkabir",
  //   lastName: "EL BAHMADI",
  //   email: "<EMAIL>",
  //   password: "Passisto@123",
  //   role: "ADMIN",
  // });
  // users.push({
  //   firstName: "Zakia",
  //   lastName: "RHIOUI",
  //   email: "<EMAIL>",
  //   password: "Passisto@123",
  //   role: "MANAGER",
  // });
  // users.push({
  //   firstName: "Yassine",
  //   lastName: "AFRACHE",
  //   email: "<EMAIL>",
  //   password: "Passisto@123",
  //   role: "ADMIN"
  // });

  // Managers (5)
  for (let i = 0; i < 0; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    users.push({
      firstName,
      lastName,
      email: faker.internet
        .email({ firstName, lastName, provider: "passisto.com" })
        .toLowerCase(),
      password: faker.internet.password({
        length: 12,
        memorable: true,
        pattern: /[A-Za-z0-9!@#$%^&*]/,
      }),
      role: "MANAGER",
    });
  }

  // Members (8)
  for (let i = 0; i < 0; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    users.push({
      firstName,
      lastName,
      email: faker.internet
        .email({ firstName, lastName, provider: "passisto.com" })
        .toLowerCase(),
      password: faker.internet.password({
        length: 12,
        memorable: true,
        pattern: /[A-Za-z0-9!@#$%^&*]/,
      }),
      role: "MEMBER",
    });
  }

  // Guests (4)
  for (let i = 0; i < 0; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    users.push({
      firstName,
      lastName,
      email: faker.internet
        .email({ firstName, lastName, provider: "passisto.com" })
        .toLowerCase(),
      password: faker.internet.password({
        length: 12,
        memorable: true,
        pattern: /[A-Za-z0-9!@#$%^&*]/,
      }),
      role: "GUEST",
    });
  }

  // Public users (2)
  for (let i = 0; i < 0; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    users.push({
      firstName,
      lastName,
      email: faker.internet
        .email({ firstName, lastName, provider: "passisto.com" })
        .toLowerCase(),
      password: faker.internet.password({
        length: 12,
        memorable: true,
        pattern: /[A-Za-z0-9!@#$%^&*]/,
      }),
      role: "PUBLIC",
    });
  }

  return users;
};

// Helper function to shuffle an array
const shuffleArray = (array) => {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
};

// Helper function to get random items from an array
const getRandomItems = (array, min, max) => {
  const count = Math.floor(Math.random() * (max - min + 1)) + min;
  return shuffleArray([...array]).slice(0, count);
};

async function seedRolesAndPermissions() {
  console.log("Seeding roles and permissions...");
  try {
    // Create roles
    const createdRoles = [];
    for (const roleName of roles) {
      const role = await prisma.role.create({
        data: { name: roleName },
      });
      console.log(`Created role: ${role.name} (${role.id})`);
      createdRoles.push(role);
    }

    // Create permissions
    const createdPermissions = [];
    for (const category of permissionCategories) {
      for (const permissionName of category.permissions) {
        const permission = await prisma.permission.create({
          data: {
            action: permissionName,
            category: category.name,
          },
        });
        console.log(
          `Created permission: ${permission.action} (${permission.id})`
        );
        createdPermissions.push(permission);
      }
    }

    // Assign all permissions to ADMIN role
    const adminRole = createdRoles.find((role) => role.name === "ADMIN");
    if (adminRole) {
      for (const permission of createdPermissions) {
        await prisma.rolePermission.create({
          data: {
            roleId: adminRole.id,
            permissionId: permission.id,
            scopeType: "GLOBAL",
            scopeId: "global",
          },
        });
      }
      console.log(`Assigned all permissions to ADMIN role`);
    }

    return { roles: createdRoles, permissions: createdPermissions };
  } catch (error) {
    console.error("Error seeding roles and permissions:", error);
    throw error;
  }
}

async function seedCompanies() {
  console.log("Seeding companies...");
  try {
    // Create company
    const company = await prisma.company.create({
      data: {
        name: companyTemplate.name,
        description: companyTemplate.description,
      },
    });

    console.log(`Created company: ${company.name} (${company.id})`);
    return company;
  } catch (error) {
    console.error("Error seeding companies:", error);
    throw error;
  }
}

async function seedUsers(company) {
  console.log("Seeding users...");
  try {
    const createdUsers = [];
    const users = generateUsers();

    for (const userData of users) {
      try {
        console.log(`Processing user: ${userData.email}`);

        // Step 1: Create invitation first
        console.log(`Creating invitation for ${userData.email}`);
        const invitation = await prisma.invitation.create({
          data: {
            email: userData.email,
            companyId: company.id,
          },
        });
        console.log(`Created invitation with ID: ${invitation.id}`);

        // Step 2: Create user in Clerk
        console.log(`Creating user in Clerk: ${userData.email}`);
        let clerkUser;
        try {
          // Create new user in Clerk
          clerkUser = await clerkClient.users.createUser({
            emailAddress: [userData.email],
            password: userData.password,
            firstName: userData.firstName,
            lastName: userData.lastName,
          });
          console.log(`Created new user in Clerk with ID: ${clerkUser.id}`);
        } catch (clerkError) {
          console.error(`Error with Clerk API: ${clerkError.message}`);
          // If we can't create in Clerk, generate a mock ID for development
          clerkUser = { id: `clerk_${uuidv4()}` };
          console.log(`Using mock Clerk ID: ${clerkUser.id}`);
        }

        // Step 3: Hash password for our database
        const hashedPassword = await bcrypt.hash(userData.password, 10);

        // Step 4: Find role
        const role = await prisma.role.findUnique({
          where: { name: userData.role },
        });

        if (!role) {
          console.warn(
            `Role ${userData.role} not found, skipping user ${userData.email}`
          );
          continue;
        }

        // Step 5: Create user in our database
        const user = await prisma.user.create({
          data: {
            clerkId: clerkUser.id,
            email: userData.email,
            firstName: userData.firstName,
            lastName: userData.lastName,
            password: hashedPassword,
            companyId: company.id,
            roles: {
              connect: { id: role.id },
            },
          },
        });

        console.log(
          `Created user: ${user.email} (${user.id}) for company ${company.name}`
        );

        // Step 6: Update Clerk user metadata
        try {
          await clerkClient.users.updateUserMetadata(clerkUser.id, {
            publicMetadata: {
              companyId: company.id,
              userId: user.id,
            },
          });
          console.log(`Updated Clerk metadata for user ${user.email}`);
        } catch (metadataError) {
          console.error(
            `Error updating Clerk metadata: ${metadataError.message}`
          );
        }

        createdUsers.push(user);
      } catch (userError) {
        console.error(`Error processing user ${userData.email}:`, userError);
      }
    }

    console.log(`Created ${createdUsers.length} users`);
    return createdUsers;
  } catch (error) {
    console.error("Error seeding users:", error);
    throw error;
  }
}

async function seedGroups(company, users, permissions) {
  console.log("Seeding groups...");
  try {
    const createdGroups = [];

    console.log(`Creating groups for company: ${company.name}`);

    // Create each group
    for (const groupTemplate of standardGroups) {
      // Create group
      const group = await prisma.group.create({
        data: {
          name: groupTemplate.name,
          description: groupTemplate.description,
          companyId: company.id,
        },
      });

      console.log(`Created group ${group.name} for company ${company.name}`);
      createdGroups.push(group);

      // If this is the Company group, add all users to it
      if (groupTemplate.name === "Company") {
        // Add all users to the Company group
        for (const user of users) {
          await prisma.userGroup.create({
            data: {
              userId: user.id,
              groupId: group.id,
            },
          });
          console.log(`Added user ${user.email} to Company group`);
        }
      } else {
        // For other groups, add random users (between 3 and 7)
        const randomUsers = getRandomItems(users, 3, 7);
        for (const user of randomUsers) {
          await prisma.userGroup.create({
            data: {
              userId: user.id,
              groupId: group.id,
            },
          });
          console.log(`Added user ${user.email} to ${group.name} group`);
        }

        // Assign random permissions to each group (except Company)
        // Between 2 and 5 permissions per group
        const randomPermissions = getRandomItems(permissions, 2, 5);
        for (const permission of randomPermissions) {
          await prisma.groupPermission.create({
            data: {
              groupId: group.id,
              permissionId: permission.id,
              scopeType: "TEAM",
              scopeId: group.id,
            },
          });
          console.log(
            `Assigned permission ${permission.action} to ${group.name} group`
          );
        }
      }
    }

    return createdGroups;
  } catch (error) {
    console.error("Error seeding groups:", error);
    throw error;
  }
}

async function seedCompaniesAndUsers() {
  try {
    // Seed roles and permissions first
    const { roles: createdRoles, permissions: createdPermissions } =
      await seedRolesAndPermissions();

    // Seed company
    // const company = await seedCompanies();

    // Seed users for the company
    // const users = await seedUsers(company);

    // Create groups for the company and add users to groups
    // const groups = await seedGroups(company, users, createdPermissions);

    // Seed Integrations
    // await seedFakeIntegrations();
    console.log("Fake integrations seeded successfully");

    return { company, users, groups };
  } catch (error) {
    console.error("Error seeding companies and users:", error);
    throw error;
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedCompaniesAndUsers()
    .then(() => {
      console.log("Companies and users seeded successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Seeding failed:", error);
      process.exit(1);
    });
}

module.exports = {
  seedCompaniesAndUsers,
  seedRolesAndPermissions,
  roles,
  permissionCategories,
};
