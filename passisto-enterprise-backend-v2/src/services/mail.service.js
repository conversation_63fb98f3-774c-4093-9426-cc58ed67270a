const nodemailer = require("nodemailer");
const fs = require("fs").promises;
const path = require("path");

const transport = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: true,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD,
  },
});

const sendInviteMail = async (email, password) => {
  const platformLink = process.env.FRONTEND_URL || 'https://enterprise.passisto.com';
  console.log(`Sending invite email with platform link: ${platformLink}`);
  
  const filePath = path.join(__dirname, "../templates/inviteTemplate.html");
  const templateContent = await fs.readFile(filePath, "utf-8");
  const emailContent = templateContent
    .replace("{{platform_link}}", platformLink)
    .replace("{{platformLink}}", platformLink)
    .replace("{{email}}", email)
    .replace("{{password}}", password);
  const mailOptions = {
    from: process.env.DEFAULT_FROM_EMAIL,
    to: email,
    subject: "You're Invited to Passisto Platform!",
    html: emailContent,
  };
  
  try {
    const info = await transport.sendMail(mailOptions);
    console.log("Invite email sent: ", info.response);
    return true;
  } catch (error) {
    console.error("Error sending invite email: ", error);
    return false;
  }
};

const sendInterviewMail = async (
  candidateName,
  jobRole,
  companyName,
  interviewLink,
  email,       
  password,  
  companyEmail
) => {
  const filePath = path.join(
    __dirname,
    "../templates/CandidateInterviewConfirmation.html"
  );
  const templateContent = await fs.readFile(filePath, "utf-8");
  let emailContent = templateContent
    .replace(/{{CANDIDAT_NAME}}/g, candidateName)
    .replace(/{{JOB_ROLE}}/g, jobRole)
    .replace(/{{COMPANY_NAME}}/g, companyName)
    .replace(/{{INTERVIEW_LINK}}/g, interviewLink)
    .replace(/{{CANDIDAT_EMAIL}}/g, email)
    .replace(/{{CANDIDAT_PASSWORD}}/g, password)
    .replace(/{{COMPANY_EMAIL}}/g, companyEmail);
  const mailOptions = {
    from: process.env.DEFAULT_FROM_EMAIL,
    to: email,  // To: email (from function call)
    subject: `Please Join Your Interview for ${jobRole} ${companyName}`,
    html: emailContent,
  };
  try {
    const info = await transport.sendMail(mailOptions);
    console.log("Invite email sent: ", info.response);
    return true;
  } catch (error) {
    console.error("Error sending invite email: ", error);
    return false;
  }
};

const sendEmailBuilder = async (fromName, to, htmlBody, subject = "Email From Passisto") => {
    const mailOptions = {
        from: `${fromName} <${process.env.DEFAULT_FROM_EMAIL}>`,
        to: to,
        subject: subject,
        html: htmlBody
    };
    
    try {
        const info = await transport.sendMail(mailOptions);
        console.log("Email sent: ", info.response);
        return true;
    } catch (error) {
        console.error("Error sending email: ", error);
        return false;
    }
};

const sendEmail = async (fromName, to, htmlBody, subject = "Email From Passisto") => {
  const mailOptions = {
      from: `${fromName} <${process.env.DEFAULT_FROM_EMAIL}>`,
      to: to,
      subject: subject,
      html: htmlBody
  };
  
  try {
      const info = await transport.sendMail(mailOptions);
      console.log("Email sent: ", info.response);
      return true;
  } catch (error) {
      console.error("Error sending email: ", error);
      return false;
  }
};
module.exports = { sendInviteMail, sendEmailBuilder, sendInterviewMail, sendEmail };
