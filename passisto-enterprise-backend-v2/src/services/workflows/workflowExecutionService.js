const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { NodeExecutorFactory } = require('./nodeExecutors');

class WorkflowExecutionService {
  constructor(io) {
    this.io = io;
    this.runningWorkflowRuns = new Map();
    this.nodeExecutorFactory = new NodeExecutorFactory(prisma, io);

    // formData column now exists in the database
    this.hasFormDataColumn = true;
  }

  async startWorkflowRun(workflowRunId) {
    try {
      // Get the workflow run
      const workflowRun = await prisma.workflowRun.findUnique({
        where: { id: workflowRunId },
        include: {
          workflow: {
            select: {
              id: true,
              name: true,
              nodes: true,
              edges: true
            }
          }
        }
      });

      if (!workflowRun) {
        throw new Error(`Workflow run ${workflowRunId} not found`);
      }

      // Update workflow run status
      await prisma.workflowRun.update({
        where: { id: workflowRunId },
        data: {
          status: 'RUNNING',
          startedAt: new Date()
        }
      });

      // Store in memory
      this.runningWorkflowRuns.set(workflowRunId, {
        startTime: new Date(),
        workflowId: workflowRun.workflowId,
        nodes: workflowRun.workflow.nodes,
        edges: workflowRun.workflow.edges,
        executionPath: []
      });

      // Emit status update
      this.io.emit('workflowRunProgress', {
        workflowRunId,
        workflowId: workflowRun.workflowId,
        status: 'RUNNING',
        message: 'Workflow execution started'
      });

      // Initialize node runs for all nodes in the workflow
      let nodes;
      try {
        nodes = typeof workflowRun.workflow.nodes === 'string'
          ? JSON.parse(workflowRun.workflow.nodes)
          : workflowRun.workflow.nodes;
      } catch (error) {
        console.error('Error parsing nodes:', error);
        throw new Error(`Unable to parse workflow nodes: ${error.message}`);
      }

      // Create node runs in parallel for better performance
      const nodeCreatePromises = [];
      for (const node of nodes) {
        nodeCreatePromises.push(
          prisma.nodeRun.create({
            data: {
              workflowRunId,
              nodeId: node.id,
              status: 'PENDING'
            }
          })
        );
      }

      await Promise.all(nodeCreatePromises);

      // Start execution of the workflow
      const executionResult = await this.executeWorkflow(workflowRunId);

      if (!executionResult) {
        // If executeWorkflow returned false, there was an error but it was handled
        console.warn(`Workflow execution for ${workflowRunId} completed with errors`);
      }

      return true;
    } catch (error) {
      console.error('Error starting workflow run:', error);

      try {
        // Update workflow run status to FAILED
        await prisma.workflowRun.update({
          where: { id: workflowRunId },
          data: {
            status: 'FAILED',
            finishedAt: new Date()
          }
        });

        // Emit error
        this.io.emit('workflowRunProgress', {
          workflowRunId,
          status: 'FAILED',
          message: `Error starting workflow: ${error.message}`
        });
      } catch (updateError) {
        console.error('Error updating workflow run status after failure:', updateError);
      }

      // Return false instead of throwing
      return false;
    }
  }

  async executeWorkflow(workflowRunId) {
    try {
      // First check if the workflow run exists in the database
      const workflowRunDb = await prisma.workflowRun.findUnique({
        where: { id: workflowRunId },
        include: {
          workflow: {
            select: {
              id: true,
              nodes: true,
              edges: true
            }
          }
        }
      });

      if (!workflowRunDb) {
        throw new Error(`Workflow run ${workflowRunId} not found in database`);
      }

      // Check if it's in memory
      let workflowRunData = this.runningWorkflowRuns.get(workflowRunId);

      // If not in memory, add it
      if (!workflowRunData) {
        workflowRunData = {
          startTime: workflowRunDb.startedAt || new Date(),
          workflowId: workflowRunDb.workflowId,
          nodes: workflowRunDb.workflow.nodes,
          edges: workflowRunDb.workflow.edges,
          executionPath: []
        };

        this.runningWorkflowRuns.set(workflowRunId, workflowRunData);
      }

      let nodes, edges;
      try {
        nodes = typeof workflowRunData.nodes === 'string'
          ? JSON.parse(workflowRunData.nodes)
          : workflowRunData.nodes;

        edges = typeof workflowRunData.edges === 'string'
          ? JSON.parse(workflowRunData.edges)
          : workflowRunData.edges;
      } catch (error) {
        console.error('Error parsing workflow data:', error);
        throw new Error(`Unable to parse workflow data: ${error.message}`);
      }

      // Find start nodes (nodes with no incoming edges)
      const startNodeIds = nodes
        .filter(node => !edges.some(edge => edge.target === node.id))
        .map(node => node.id);

      // Execute each start node
      const nodePromises = [];
      for (const nodeId of startNodeIds) {
        nodePromises.push(this.executeNode(workflowRunId, nodeId));
      }

      // Wait for all start nodes to complete
      await Promise.all(nodePromises);

      // Check if all nodes are complete
      const allNodeRuns = await prisma.nodeRun.findMany({
        where: { workflowRunId }
      });

      const allNodesComplete = allNodeRuns.every(run =>
        run.status === 'SUCCESS' || run.status === 'FAILED' || run.status === 'SKIPPED'
      );

      if (allNodesComplete) {
        const anyNodeFailed = allNodeRuns.some(run => run.status === 'FAILED');
        await this.completeWorkflowRun(workflowRunId, !anyNodeFailed);
      }

      return true;
    } catch (error) {
      console.error('Error executing workflow:', error);

      try {
        await this.completeWorkflowRun(workflowRunId, false, error);
      } catch (completeError) {
        console.error('Error completing workflow run after execution error:', completeError);
      }

      // Return false instead of throwing
      return false;
    }
  }

  async executeNode(workflowRunId, nodeId) {
    try {
      // First check if the node run exists in the database
      const existingNodeRun = await prisma.nodeRun.findFirst({
        where: {
          workflowRunId: workflowRunId,
          nodeId: nodeId
        }
      });

      if (!existingNodeRun) {
        console.error(`Node run for workflow ${workflowRunId}, node ${nodeId} not found in database`);
        return false;
      }

      // Check if the workflow run exists in memory
      const workflowRunData = this.runningWorkflowRuns.get(workflowRunId);

      // If not in memory, try to retrieve from database and add to memory
      if (!workflowRunData) {
        const workflowRun = await prisma.workflowRun.findUnique({
          where: { id: workflowRunId },
          include: {
            workflow: {
              select: {
                id: true,
                nodes: true,
                edges: true
              }
            }
          }
        });

        if (workflowRun) {
          // Recreate the in-memory representation
          this.runningWorkflowRuns.set(workflowRunId, {
            startTime: workflowRun.startedAt || new Date(),
            workflowId: workflowRun.workflowId,
            nodes: workflowRun.workflow.nodes,
            edges: workflowRun.workflow.edges,
            executionPath: [],
            currentNodeId: nodeId // Track the current node being executed
          });
        } else {
          console.error(`Workflow run ${workflowRunId} not found in database`);
          return false;
        }
      } else {
        // Update the current node ID in memory
        workflowRunData.currentNodeId = nodeId;
      }

      // Find the node in the workflow
      let nodes;
      try {
        // Get the latest data from the Map
        const latestWorkflowRunData = this.runningWorkflowRuns.get(workflowRunId);
        if (!latestWorkflowRunData) {
          throw new Error(`Workflow run data not found in memory for ${workflowRunId}`);
        }

        nodes = typeof latestWorkflowRunData.nodes === 'string'
          ? JSON.parse(latestWorkflowRunData.nodes)
          : latestWorkflowRunData.nodes;
      } catch (error) {
        console.error('Error parsing nodes:', error);
        throw new Error(`Unable to parse nodes: ${error.message}`);
      }

      const currentNode = nodes.find(node => node.id === nodeId);

      if (!currentNode) {
        throw new Error(`Node ${nodeId} not found in workflow run ${workflowRunId}`);
      }

      // Check if this is a user task node that requires user assignment
      if (currentNode.type === 'task' && currentNode.data && (currentNode.data.assignee || currentNode.data.assigneeId)) {
        // Get assignee ID from either assignee or assigneeId field in node data
        const assigneeId = currentNode.data.assignee || currentNode.data.assigneeId;
        const formFields = currentNode.data.formFields || [];
        const selectedContextVars = currentNode.data.selectedContextVars || [];

        // Get inputs from previous nodes for contextual data
        const inputs = await this.getNodeInputs(workflowRunId, nodeId);

        // Check if the user exists
        const assignedUser = await prisma.user.findUnique({
          where: { id: assigneeId }
        });

        if (!assignedUser) {
          throw new Error(`Assigned user with ID ${assigneeId} not found`);
        }

        // Update node run status to WAITING_FOR_USER and assign the user
        await prisma.nodeRun.update({
          where: {
            workflowRunId_nodeId: {
              workflowRunId: workflowRunId,
              nodeId: nodeId
            }
          },
          data: {
            status: 'WAITING_FOR_USER',
            startedAt: new Date(),
            assignedAt: new Date(),
            assigneeId: assigneeId, // This is the correct field name in the schema
            formData: { fields: formFields }, // Store form fields in the formData field
            output: {
              taskDetails: {
                title: currentNode.data.label || 'Task',
                description: currentNode.data.description || '',
                formFields: formFields,
                contextualData: this.processContextualData(inputs, selectedContextVars)
              }
            }
          }
        });

        // Update workflow run status to WAITING_FOR_USER
        await prisma.workflowRun.update({
          where: { id: workflowRunId },
          data: {
            status: 'WAITING_FOR_USER'
          }
        });

        // Update execution path - get the latest data from the Map
        const updatedWorkflowRunData = this.runningWorkflowRuns.get(workflowRunId);
        if (updatedWorkflowRunData) {
          updatedWorkflowRunData.executionPath.push(nodeId);
        }

        // Emit progress with enhanced data including form fields and contextual data
        this.io.emit('nodeRunProgress', {
          workflowRunId,
          nodeId,
          status: 'WAITING_FOR_USER',
          message: `Waiting for user ${assignedUser.firstName} to complete task`,
          assigneeId: assigneeId,
          assigneeName: assignedUser.firstName,
          formFields: formFields,
          contextualData: this.processContextualData(inputs, selectedContextVars),
          taskDetails: {
            title: currentNode.data.label || 'Task',
            description: currentNode.data.description || ''
          }
        });

        // Emit workflow status update
        this.io.emit('workflowRunProgress', {
          workflowRunId,
          workflowId: workflowRunData.workflowId,
          status: 'WAITING_FOR_USER',
          currentNodeId: nodeId,
          message: `Workflow paused, waiting for user ${assignedUser.firstName} to complete task`
        });

        // Return true but don't proceed to next nodes yet
        return true;
      }

      // For non-user-task nodes or if we're resuming execution after user completion
      // Update node run status to RUNNING
      await prisma.nodeRun.update({
        where: {
          workflowRunId_nodeId: {
            workflowRunId: workflowRunId,
            nodeId: nodeId
          }
        },
        data: {
          status: 'RUNNING',
          startedAt: new Date()
        }
      });

      // Update execution path - get the latest data from the Map
      const updatedWorkflowRunData = this.runningWorkflowRuns.get(workflowRunId);
      if (updatedWorkflowRunData) {
        updatedWorkflowRunData.executionPath.push(nodeId);
      }

      // Emit progress with enhanced data
      this.io.emit('nodeRunProgress', {
        workflowRunId,
        nodeId,
        status: 'RUNNING',
        message: `Executing node ${nodeId}`
      });

      // Get inputs from previous nodes
      const inputs = await this.getNodeInputs(workflowRunId, nodeId);

      // Log the inputs
      console.log(`\n[Node Execution] Node ${nodeId} (${currentNode.type}) - INPUTS:`, JSON.stringify(inputs, null, 2));

      // Get the appropriate executor for this node type
      const executor = this.nodeExecutorFactory.getExecutor(currentNode.type);

      // Execute the node
      let { output, success, error, waiting } = await executor.execute(workflowRunId, nodeId, currentNode, inputs);

      // If the node is waiting for user input, we've already updated the status and emitted events
      if (waiting) {
        return true;
      }

      // Log the outputs
      executor.logExecution(nodeId, currentNode.type, inputs, output);

      // Create a detailed execution log
      const executionLog = executor.createExecutionLog(nodeId, currentNode.type, inputs, output, success, error);

      // Log the execution details to a file or database if needed
      console.log(`[Node Execution] EXECUTION SUMMARY:\n${JSON.stringify(executionLog, null, 2)}\n`);

      // Emit the execution details via WebSockets
      executor.emitExecutionDetails(workflowRunId, nodeId, executionLog);

      // Update node run status based on execution result
      await executor.updateNodeRunStatus(workflowRunId, nodeId, success, output, error);

      // Emit node completion
      executor.emitNodeCompletion(workflowRunId, nodeId, success, output, error);

      // For decision nodes, update the node data with the output
      if (currentNode.type === 'decision' && success) {
        // Emit a special event for decision nodes to update the UI
        this.io.emit('decisionNodeResult', {
          workflowRunId,
          nodeId,
          path: output.path,
          output
        });
      }

      // If successful, find and execute next nodes
      if (success) {
        let edges;
        try {
          // Get the latest data from the Map
          const latestWorkflowRunData = this.runningWorkflowRuns.get(workflowRunId);
          if (!latestWorkflowRunData) {
            throw new Error(`Workflow run data not found in memory for ${workflowRunId}`);
          }

          edges = typeof latestWorkflowRunData.edges === 'string'
            ? JSON.parse(latestWorkflowRunData.edges)
            : latestWorkflowRunData.edges;
        } catch (error) {
          console.error('Error parsing edges:', error);
          throw new Error(`Unable to parse edges: ${error.message}`);
        }

        // For decision nodes, we need to filter edges based on the decision path
        let nextEdges = [];

        if (currentNode.type === 'decision' && output && output.path) {
          // Get the decision path from the output
          const decisionPath = output.path;

          // Filter edges based on the decision path (yes or no)
          nextEdges = edges.filter(edge => {
            return edge.source === nodeId &&
                  (edge.id === decisionPath ||
                   edge.label === decisionPath ||
                   (decisionPath === 'yes' && edge.sourceHandle === 'yes') ||
                   (decisionPath === 'no' && edge.sourceHandle === 'no'));
          });

          console.log(`[Decision Node] Following path: ${decisionPath}, found ${nextEdges.length} matching edges`);

          // Mark nodes on non-selected paths as SKIPPED
          await this.markSkippedNodes(workflowRunId, nodeId, nextEdges, edges, nodes);

          // If no matching edges found, log a warning
          // if (nextEdges.length === 0) {
          //   console.warn(`[Decision Node] No edges found for path: ${decisionPath}, falling back to all outgoing edges`);
          //   nextEdges = edges.filter(edge => edge.source === nodeId);
          // }
        } else {
          // For non-decision nodes, get all outgoing edges
          nextEdges = edges.filter(edge => edge.source === nodeId);
        }

        for (const edge of nextEdges) {
          await this.executeNode(workflowRunId, edge.target);
        }
      }

      // Check if workflow is complete (all nodes have been processed)
      const allNodeRuns = await prisma.nodeRun.findMany({
        where: { workflowRunId }
      });

      const allNodesComplete = allNodeRuns.every(run =>
        run.status === 'SUCCESS' || run.status === 'FAILED' || run.status === 'SKIPPED'
      );

      const anyNodeWaiting = allNodeRuns.some(run => run.status === 'WAITING_FOR_USER');

      if (allNodesComplete && !anyNodeWaiting) {
        const anyNodeFailed = allNodeRuns.some(run => run.status === 'FAILED');
        await this.completeWorkflowRun(workflowRunId, !anyNodeFailed);
      }

      return success;
    } catch (error) {
      console.error(`Error executing node ${nodeId}:`, error);

      try {
        // Update node run status
        await prisma.nodeRun.update({
          where: {
            workflowRunId_nodeId: {
              workflowRunId: workflowRunId,
              nodeId: nodeId
            }
          },
          data: {
            status: 'FAILED',
            finishedAt: new Date(),
            output: { error: error.message }
          }
        });

        // Check if workflow should complete
        const allNodeRuns = await prisma.nodeRun.findMany({
          where: { workflowRunId }
        });

        const allNodesComplete = allNodeRuns.every(run =>
          run.status === 'SUCCESS' || run.status === 'FAILED' || run.status === 'SKIPPED'
        );

        const anyNodeWaiting = allNodeRuns.some(run => run.status === 'WAITING_FOR_USER');

        if (allNodesComplete && !anyNodeWaiting) {
          // Don't throw errors from completeWorkflowRun
          await this.completeWorkflowRun(workflowRunId, false, error).catch(err => {
            console.error(`Error completing workflow run ${workflowRunId}:`, err);
          });
        }
      } catch (updateError) {
        console.error(`Error updating node run status for node ${nodeId}:`, updateError);
      }

      // Return false instead of throwing to allow other nodes to continue
      return false;
    }
  }

  /**
   * Get inputs for a node from its predecessor nodes
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node to get inputs for
   * @returns {Promise<Object>} - The inputs for the node
   */
  async getNodeInputs(workflowRunId, nodeId) {
    try {
      // Get the workflow run data
      const workflowRunData = this.runningWorkflowRuns.get(workflowRunId);
      if (!workflowRunData) {
        console.error(`Workflow run data not found in memory for ${workflowRunId}`);
        return {};
      }

      // Parse nodes and edges
      let nodes, edges;
      try {
        nodes = typeof workflowRunData.nodes === 'string'
          ? JSON.parse(workflowRunData.nodes)
          : workflowRunData.nodes;

        edges = typeof workflowRunData.edges === 'string'
          ? JSON.parse(workflowRunData.edges)
          : workflowRunData.edges;
      } catch (error) {
        console.error('Error parsing workflow data:', error);
        return {};
      }

      // Check if this is a task node - if so, we need to get all upstream nodes
      const currentNode = nodes.find(node => node.id === nodeId);
      const isTaskNode = currentNode?.type === 'task';

      if (isTaskNode) {
        // For task nodes, get all upstream nodes recursively
        console.log(`Getting all upstream nodes for task node ${nodeId}`);
        return await this.getAllUpstreamNodeInputs(workflowRunId, nodeId, edges);
      }

      // For non-task nodes, just get direct predecessors as before
      // Find incoming edges to this node
      const incomingEdges = edges.filter(edge => edge.target === nodeId);

      if (incomingEdges.length === 0) {
        // This is a start node or has no inputs
        return { isStartNode: true };
      }

      // Get outputs from all predecessor nodes
      const inputs = {};

      for (const edge of incomingEdges) {
        const sourceNodeId = edge.source;

        // Get the node run for the source node
        const sourceNodeRun = await prisma.nodeRun.findFirst({
          where: {
            workflowRunId: workflowRunId,
            nodeId: sourceNodeId
          }
        });

        if (sourceNodeRun && sourceNodeRun.output) {
          // Add the output to the inputs object
          let sourceOutput;
          try {
            sourceOutput = typeof sourceNodeRun.output === 'string'
              ? JSON.parse(sourceNodeRun.output)
              : sourceNodeRun.output;
          } catch (error) {
            console.error(`Error parsing output for node ${sourceNodeId}:`, error);
            sourceOutput = { error: 'Failed to parse output' };
          }

          inputs[sourceNodeId] = sourceOutput;
        }
      }

      return inputs;
    } catch (error) {
      console.error(`Error getting inputs for node ${nodeId}:`, error);
      return { error: error.message };
    }
  }

  /**
   * Get all upstream node inputs recursively
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node to get inputs for
   * @param {Array} edges - All edges in the workflow
   * @returns {Promise<Object>} - All upstream node inputs
   */
  async getAllUpstreamNodeInputs(workflowRunId, nodeId, edges) {
    try {
      // Use a set to track visited nodes and avoid infinite loops
      const visited = new Set();
      const inputs = {};

      // Recursive function to find all upstream nodes
      const findUpstreamNodes = async (currentNodeId) => {
        if (visited.has(currentNodeId)) return;
        visited.add(currentNodeId);

        // Find all incoming edges to this node
        const incomingEdges = edges.filter(edge => edge.target === currentNodeId);

        if (incomingEdges.length === 0) {
          // This is a start node
          return;
        }

        // Process each source node
        for (const edge of incomingEdges) {
          const sourceNodeId = edge.source;

          // Get the node run for the source node
          const sourceNodeRun = await prisma.nodeRun.findFirst({
            where: {
              workflowRunId: workflowRunId,
              nodeId: sourceNodeId
            }
          });

          if (sourceNodeRun && sourceNodeRun.output) {
            // Add the output to the inputs object if not already added
            if (!inputs[sourceNodeId]) {
              let sourceOutput;
              try {
                sourceOutput = typeof sourceNodeRun.output === 'string'
                  ? JSON.parse(sourceNodeRun.output)
                  : sourceNodeRun.output;
              } catch (error) {
                console.error(`Error parsing output for node ${sourceNodeId}:`, error);
                sourceOutput = { error: 'Failed to parse output' };
              }

              inputs[sourceNodeId] = sourceOutput;
            }
          }

          // Recursively find upstream nodes of this source node
          await findUpstreamNodes(sourceNodeId);
        }
      };

      // Start the recursive search
      await findUpstreamNodes(nodeId);

      // If no inputs were found, return isStartNode: true
      if (Object.keys(inputs).length === 0) {
        return { isStartNode: true };
      }

      console.log(`Found ${Object.keys(inputs).length} upstream nodes for task node ${nodeId}`);
      return inputs;
    } catch (error) {
      console.error(`Error getting all upstream inputs for node ${nodeId}:`, error);
      return { error: error.message };
    }
  }

  async completeWorkflowRun(workflowRunId, success = true, error = null) {
    try {
      // Check if workflow run exists in database regardless of in-memory state
      const workflowRun = await prisma.workflowRun.findUnique({
        where: { id: workflowRunId }
      });

      if (!workflowRun) {
        throw new Error(`Workflow run ${workflowRunId} not found in database`);
      }

      // Get in-memory data if available
      const workflowRunData = this.runningWorkflowRuns.get(workflowRunId);
      let workflowId = workflowRun.workflowId; // Default to DB value

      if (workflowRunData) {
        workflowId = workflowRunData.workflowId; // Use in-memory value if available
      }

      // Update workflow run in database
      await prisma.workflowRun.update({
        where: { id: workflowRunId },
        data: {
          status: success ? 'SUCCESS' : 'FAILED',
          finishedAt: new Date()
        }
      });

      // Check if all nodes are complete before removing from memory
      const allNodeRuns = await prisma.nodeRun.findMany({
        where: { workflowRunId }
      });

      const allNodesComplete = allNodeRuns.every(run =>
        run.status === 'SUCCESS' || run.status === 'FAILED' || run.status === 'SKIPPED'
      );

      if (allNodesComplete) {
        // Only remove from running workflows if all nodes are complete
        this.runningWorkflowRuns.delete(workflowRunId);
      }

      // Emit completion via workflowRunProgress
      this.io.emit('workflowRunProgress', {
        workflowRunId,
        workflowId,
        status: success ? 'SUCCESS' : 'FAILED',
        message: success ? 'Workflow execution completed successfully' : `Workflow execution failed: ${error?.message || 'One or more nodes failed'}`
      });

      // Also emit a specific workflowCompleted event to ensure all nodes are reset
      this.io.emit('workflowCompleted', {
        workflowRunId,
        workflowId,
        status: success ? 'SUCCESS' : 'FAILED'
      });

      return true;
    } catch (error) {
      console.error('Error completing workflow run:', error);

      // Try to update the workflow run status in the database even if there was an error
      try {
        await prisma.workflowRun.update({
          where: { id: workflowRunId },
          data: {
            status: 'FAILED',
            finishedAt: new Date()
          }
        });
      } catch (dbError) {
        console.error('Failed to update workflow run status:', dbError);
      }

      // Don't throw the error, just log it and return false
      return false;
    }
  }

  async stopWorkflowRun(workflowRunId) {
    try {
      const workflowRunData = this.runningWorkflowRuns.get(workflowRunId);
      if (!workflowRunData) {
        throw new Error(`Workflow run ${workflowRunId} not found in running workflows`);
      }

      // Update workflow run
      await prisma.workflowRun.update({
        where: { id: workflowRunId },
        data: {
          status: 'FAILED',
          finishedAt: new Date()
        }
      });

      // Update all running and waiting node runs to FAILED
      await prisma.nodeRun.updateMany({
        where: {
          workflowRunId,
          status: { in: ['RUNNING', 'WAITING_FOR_USER'] }
        },
        data: {
          status: 'FAILED',
          finishedAt: new Date(),
          output: { message: 'Execution stopped manually' }
        }
      });

      // Remove from running workflows
      this.runningWorkflowRuns.delete(workflowRunId);

      // Emit stop event
      this.io.emit('workflowRunProgress', {
        workflowRunId,
        workflowId: workflowRunData.workflowId,
        status: 'FAILED',
        message: 'Workflow execution stopped'
      });

      // Also emit a specific workflowCompleted event to ensure all nodes are reset
      this.io.emit('workflowCompleted', {
        workflowRunId,
        workflowId: workflowRunData.workflowId,
        status: 'FAILED'
      });

      // Emit node run progress events for all waiting nodes
      const nodeRuns = await prisma.nodeRun.findMany({
        where: { workflowRunId }
      });

      // Notify about each node that was waiting for user input
      for (const nodeRun of nodeRuns) {
        if (nodeRun.status === 'FAILED' && nodeRun.assigneeId) {
          this.io.emit('nodeRunProgress', {
            workflowRunId,
            nodeId: nodeRun.nodeId,
            status: 'FAILED',
            message: 'Task canceled because workflow was stopped',
            assigneeId: nodeRun.assigneeId
          });
        }
      }

      return true;
    } catch (error) {
      console.error('Error stopping workflow run:', error);
      throw error;
    }
  }

  getWorkflowRunStatus(workflowRunId) {
    return this.runningWorkflowRuns.get(workflowRunId);
  }

  isWorkflowRunRunning(workflowRunId) {
    return this.runningWorkflowRuns.has(workflowRunId);
  }

  /**
   * Mark nodes that will be skipped due to decision node branching
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} decisionNodeId - The ID of the decision node
   * @param {Array} selectedEdges - The edges that were selected by the decision
   * @param {Array} allEdges - All edges in the workflow
   * @param {Array} allNodes - All nodes in the workflow
   * @returns {Promise<void>}
   */
  async markSkippedNodes(workflowRunId, decisionNodeId, selectedEdges, allEdges, allNodes) {
    try {
      // Get all outgoing edges from the decision node
      const allOutgoingEdges = allEdges.filter(edge => edge.source === decisionNodeId);

      // Find edges that were not selected (skipped paths)
      const skippedEdges = allOutgoingEdges.filter(edge =>
        !selectedEdges.some(selectedEdge => selectedEdge.id === edge.id)
      );

      if (skippedEdges.length === 0) {
        return; // No skipped paths
      }

      console.log(`[Decision Node] Marking nodes on skipped paths as SKIPPED. Found ${skippedEdges.length} skipped edges`);

      // For each skipped edge, find all downstream nodes
      for (const skippedEdge of skippedEdges) {
        const targetNodeId = skippedEdge.target;
        await this.markNodeAndDownstreamAsSkipped(workflowRunId, targetNodeId, allEdges, allNodes, new Set());
      }
    } catch (error) {
      console.error(`Error marking skipped nodes for workflow ${workflowRunId}:`, error);
    }
  }

  /**
   * Recursively mark a node and all its downstream nodes as SKIPPED
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node to mark as skipped
   * @param {Array} allEdges - All edges in the workflow
   * @param {Array} allNodes - All nodes in the workflow
   * @param {Set} visitedNodes - Set of already visited nodes to prevent infinite loops
   * @returns {Promise<void>}
   */
  async markNodeAndDownstreamAsSkipped(workflowRunId, nodeId, allEdges, allNodes, visitedNodes) {
    // Prevent infinite loops in case of cycles
    if (visitedNodes.has(nodeId)) {
      return;
    }
    visitedNodes.add(nodeId);

    // Mark the current node as SKIPPED if it's in PENDING state
    const nodeRun = await prisma.nodeRun.findFirst({
      where: {
        workflowRunId,
        nodeId,
        status: 'PENDING' // Only mark nodes that haven't been executed yet
      }
    });

    if (nodeRun) {
      await prisma.nodeRun.update({
        where: {
          workflowRunId_nodeId: {
            workflowRunId,
            nodeId
          }
        },
        data: {
          status: 'SKIPPED',
          finishedAt: new Date(),
          output: { message: 'Node skipped due to decision node branching' }
        }
      });

      // Emit node skipped event
      this.io.emit('nodeRunProgress', {
        workflowRunId,
        nodeId,
        status: 'SKIPPED',
        message: 'Node skipped due to decision node branching'
      });
    }

    // Find all outgoing edges from this node
    const outgoingEdges = allEdges.filter(edge => edge.source === nodeId);

    // Recursively mark all downstream nodes
    for (const edge of outgoingEdges) {
      await this.markNodeAndDownstreamAsSkipped(workflowRunId, edge.target, allEdges, allNodes, visitedNodes);
    }
  }

  /**
   * Process contextual data from inputs based on selected variables
   * @param {Object} inputs - The inputs from previous nodes
   * @param {Array} selectedContextVars - Array of selected context variables
   * @returns {Object} - Processed contextual data
   */
  processContextualData(inputs, selectedContextVars) {
    if (!inputs || Object.keys(inputs).length === 0 || inputs.isStartNode) {
      return {};
    }

    // If no specific variables are selected, return empty object
    if (!selectedContextVars || selectedContextVars.length === 0) {
      return {};
    }

    const contextualData = {};

    // Process each selected context variable
    selectedContextVars.forEach(contextVar => {
      const { nodeId, key, label } = contextVar;

      // Check if we have data from this node
      if (inputs[nodeId]) {
        // Extract the specific key from the node output
        // If key is 'result', get the result directly, otherwise navigate the output object
        let value;
        if (key === 'result') {
          value = inputs[nodeId].result;
        } else if (key.includes('.')) {
          // Handle nested paths like 'output.text'
          const parts = key.split('.');
          let current = inputs[nodeId];
          for (const part of parts) {
            if (current && typeof current === 'object') {
              current = current[part];
            } else {
              current = undefined;
              break;
            }
          }
          value = current;
        } else {
          value = inputs[nodeId][key];
        }

        // Add to contextual data with the provided label
        if (value !== undefined) {
          contextualData[label || key] = value;
        }
      }
    });

    return contextualData;
  }

  /**
   * Complete a user task and resume workflow execution
   * @param {string} workflowRunId - The ID of the workflow run
   * @param {string} nodeId - The ID of the node to complete
   * @param {string} userId - The ID of the user completing the task
   * @param {Object} taskData - Optional data from the task completion
   * @returns {Promise<boolean>} - Whether the task was completed successfully
   */
  async completeUserTask(workflowRunId, nodeId, userId, taskData = {}) {
    try {
      // Check if the node run exists and is in WAITING_FOR_USER state
      const nodeRun = await prisma.nodeRun.findFirst({
        where: {
          workflowRunId,
          nodeId,
          status: 'WAITING_FOR_USER'
        },
        include: {
          assignee: {
            select: {
              id: true,
              firstName: true,
              email: true
            }
          }
        }
      });

      if (!nodeRun) {
        throw new Error(`Node run for workflow ${workflowRunId}, node ${nodeId} not found or not in waiting state`);
      }

      // Check if the user is authorized to complete this task
      if (nodeRun.assigneeId !== userId) {
        throw new Error(`User ${userId} is not authorized to complete this task. It's assigned to ${nodeRun.assignee?.firstName || 'another user'}`);
      }

      // Get any files attached to this node run
      const files = await prisma.file.findMany({
        where: {
          nodeRunId: `${workflowRunId}-${nodeId}`
        }
      });

      // Get the form fields and contextual data from the node run
      const formFields = nodeRun.formData?.fields || nodeRun.output?.taskDetails?.formFields || [];
      const contextualData = nodeRun.output?.taskDetails?.contextualData || {};

      // Validate required form fields if they exist
      if (formFields.length > 0) {
        const requiredFields = formFields.filter(field => field.required);
        for (const field of requiredFields) {
          if (!taskData[field.id] && taskData[field.id] !== false) {
            throw new Error(`Required field '${field.label || field.id}' is missing`);
          }
        }
      }

      // Update the node run to SUCCESS status
      await prisma.nodeRun.update({
        where: {
          workflowRunId_nodeId: {
            workflowRunId,
            nodeId
          }
        },
        data: {
          status: 'SUCCESS',
          finishedAt: new Date(),
          completedBy: userId,
          formData: {
            fields: formFields,
            values: taskData
          },
          output: {
            ...taskData,
            completedBy: userId,
            contextualData, // Preserve contextual data
            files: files.length > 0 ? files.map(f => ({
              id: f.id,
              filename: f.filename,
              originalName: f.originalName,
              url: f.url,
              mimetype: f.mimetype,
              size: f.size
            })) : []
          }
        }
      });

      // Emit node completion event with form data and contextual data
      this.io.emit('nodeRunProgress', {
        workflowRunId,
        nodeId,
        status: 'SUCCESS',
        message: `Task completed by user ${nodeRun.assignee?.name || userId}`,
        output: taskData,
        formData: {
          fields: formFields,
          values: taskData
        },
        contextualData // Include contextual data in the event
      });

      // Update workflow run status back to RUNNING
      await prisma.workflowRun.update({
        where: { id: workflowRunId },
        data: {
          status: 'RUNNING'
        }
      });

      // Emit workflow status update
      this.io.emit('workflowRunProgress', {
        workflowRunId,
        status: 'RUNNING',
        message: 'Workflow execution resumed after user task completion'
      });

      // Find and execute next nodes
      // Get the workflow data
      const workflowRunData = this.runningWorkflowRuns.get(workflowRunId);

      if (!workflowRunData) {
        // If not in memory, try to retrieve from database
        const workflowRun = await prisma.workflowRun.findUnique({
          where: { id: workflowRunId },
          include: {
            workflow: {
              select: {
                id: true,
                nodes: true,
                edges: true
              }
            }
          }
        });

        if (workflowRun) {
          // Recreate the in-memory representation
          this.runningWorkflowRuns.set(workflowRunId, {
            startTime: workflowRun.startedAt || new Date(),
            workflowId: workflowRun.workflowId,
            nodes: workflowRun.workflow.nodes,
            edges: workflowRun.workflow.edges,
            executionPath: [],
            currentNodeId: nodeId
          });
        } else {
          throw new Error(`Workflow run ${workflowRunId} not found in database`);
        }
      }

      // Get the edges to find next nodes
      let edges;
      try {
        // Get the latest data from the Map
        const latestWorkflowRunData = this.runningWorkflowRuns.get(workflowRunId);
        if (!latestWorkflowRunData) {
          throw new Error(`Workflow run data not found in memory for ${workflowRunId}`);
        }

        edges = typeof latestWorkflowRunData.edges === 'string'
          ? JSON.parse(latestWorkflowRunData.edges)
          : latestWorkflowRunData.edges;
      } catch (error) {
        console.error('Error parsing edges:', error);
        throw new Error(`Unable to parse edges: ${error.message}`);
      }

      // For decision nodes, we need to filter edges based on the decision path
      let nextEdges = [];

      // Since we're in the catch block, we don't have the output, so we'll use all edges
      nextEdges = edges.filter(edge => edge.source === nodeId);

      // Execute each next node
      for (const edge of nextEdges) {
        await this.executeNode(workflowRunId, edge.target);
      }

      // Check if workflow is complete
      const allNodeRuns = await prisma.nodeRun.findMany({
        where: { workflowRunId }
      });

      const allNodesComplete = allNodeRuns.every(run =>
        run.status === 'SUCCESS' || run.status === 'FAILED' || run.status === 'SKIPPED'
      );

      const anyNodeWaiting = allNodeRuns.some(run => run.status === 'WAITING_FOR_USER');

      if (allNodesComplete && !anyNodeWaiting) {
        const anyNodeFailed = allNodeRuns.some(run => run.status === 'FAILED');
        await this.completeWorkflowRun(workflowRunId, !anyNodeFailed);
      }

      return true;
    } catch (error) {
      console.error(`Error completing user task for workflow ${workflowRunId}, node ${nodeId}:`, error);
      throw error;
    }
  }
}

module.exports = WorkflowExecutionService;