const express = require("express");
const checkPermission = require("@/middlewares/permMiddleware");
const { authMiddleware } = require("@/middlewares/authMiddleware");
const { performSearch } = require("@/controllers/search.controller");

const router = express.Router();
router.use(authMiddleware)
// router.use((req, res, next)=>{
//     userId = req.headers['userid'];
//     companyId = req.headers['companyid']
//     req.user = { userId };
//     req.company = { companyId }
//     next();
//   });

// Define the search route with permission checks
router.post("/", checkPermission(["CAN_SEARCH"]), performSearch);

module.exports = router;