// routes/clerkWebhookRouter.js
const express = require('express');
const { enhanceDescription, generateForm } = require('../controllers/form-builder.controller');
const { authMiddleware } = require('../middlewares/authMiddleware');


const formBuilderRouter = express.Router();

formBuilderRouter.post('/enhance-description', enhanceDescription);
formBuilderRouter.post('/generate-from', authMiddleware, generateForm);

module.exports = formBuilderRouter;
