// filepath: /passisto-enterprise/worktrees/merge-main/src/routes/chatbot.routes.js
const express = require("express");
const checkPermission = require('@/middlewares/permMiddleware');
const { authMiddleware } = require('@/middlewares/authMiddleware');
const {
  sendMessage,
  getChatHistory,
  getUserSessions,
  deleteSession
} = require("@/controllers/chatbot.controller");

const router = express.Router();
router.use(authMiddleware)
// router.use((req, res, next)=>{
//   userId = req.headers['userid'];
//   companyId = req.headers['companyid']
//   req.user = { userId };
//   req.company = { companyId }
//   next();
// });

router.post("/", checkPermission(["CAN_ASK_CHATBOT"]), sendMessage);
router.get("/history/:session_id", checkPermission(["CAN_ASK_CHATBOT"]), getChatHistory);
router.get("/user-sessions", checkPermission(["CAN_ASK_CHATBOT"]), getUserSessions);
router.delete("/history/:session_id", checkPermission(["CAN_ASK_CHATBOT"]), deleteSession);

module.exports = router;