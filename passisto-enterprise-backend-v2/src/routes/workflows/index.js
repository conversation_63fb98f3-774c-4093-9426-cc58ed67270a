
const express = require('express');

const workflowRoutes = require('./workflowRoutes');
const workflowRunRoutes = require('./workflowRunRoutes');
const nodeRunRoutes = require('./nodeRunRoutes');
const userRoutes = require('./userRoutes');
// const authRoutes = require('./routes/auth');
const taskRoutes = require('./taskRoutes');
const fileRoutes = require('./fileRoutes');
const speechToTextRoutes = require('./speechToTextRoutes');

const workflowsRouter = express.Router();

// app.use('/api/auth', authRoutes);
workflowsRouter.use('/api/workflows', workflowRoutes);
workflowsRouter.use('/api/workflow-runs', workflowRunRoutes);
workflowsRouter.use('/api/node-runs', nodeRunRoutes);
workflowsRouter.use('/api/users', userRoutes);
workflowsRouter.use('/api/tasks', taskRoutes);
workflowsRouter.use('/api/files', fileRoutes);
workflowsRouter.use('/api/speech-to-text', speechToTextRoutes);

module.exports = workflowsRouter;