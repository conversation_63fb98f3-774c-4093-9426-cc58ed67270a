const prisma = require("@/config/db");

const authMiddleware = async (req, res, next) => {

    // if (process.env.NODE_ENV === "development") {
    //   console.log("Auth Middleware: ", req.auth);
    // }

    if (!req.auth || !req.auth.userId) {
      return res.status(401).json({ error: "Unauthorized" });
    }
    const clerkUserId = req.auth.userId;
    const companyId = req.auth.sessionClaims?.public_metadata?.companyId;
    const userId = req.auth.sessionClaims?.public_metadata?.userId;
    console.log(companyId);
    if (!companyId) {
      console.log("Auth Middleware: ", req.auth);
      return res.status(403).json({ error: "Forbidden: No companyId found" });
    }

    const user = await prisma.user.findUnique({
        where: { id: userId },
        include: { company: true },
    });

    if (!user) {
      return res.status(403).json({ error: "Forbidden: User not found" });
    }

    if (user.companyId!== companyId) {
      return res.status(403).json({ error: "Forbidden: User does not belong to the company" });
    }
    req.user = { userId, clerkUserId, companyId };

    next();
};


module.exports = { authMiddleware };
