#!/bin/sh
set -e


# Generate Prisma client
echo "Generating Prisma client..."
npx prisma generate

# Run Prisma migrations
# echo "Running Prisma migrations..."
# npx prisma migrate dev --name merge --skip-seed

# Seed the database (optional)
# echo "Seeding database..."
# npm run db:truncate
# node src/seeders/optinal-seeder.js
# npm run db:reset

# Start the application
echo "Starting application..."
exec "$@"