# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings


DB_HOST=localhost
DB_USER=postgres
DB_PASSWORD=K5EadzuxNRaIGO0
DB_PORT=5432
DB_NAME=passisto
# DB_NAME=BACKEND

CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsucGFzc2lzdG8uY29tJA

DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?schema=public"
MONGO_URI="********************************************************************"


CLERK_PUBLISHABLE_KEY==pk_live_Y2xlcmsucGFzc2lzdG8uY29tJA
CLERK_SECRET_KEY=**************************************************
CLERK_WEBHOOK_SECRET=whsec_TOIRz6K03KBkf1qsFJRNpuWfb+TG/37+

EMAIL_HOST = "mail.privateemail.com"
EMAIL_PORT = 465
EMAIL_USER = "<EMAIL>"
EMAIL_PASSWORD = "informatica1E..."
EMAIL_USE_SSL = True
EMAIL_USE_TLS = False
DEFAULT_FROM_EMAIL = "Passisto <<EMAIL>>"

SMTP_HOST= "mail.privateemail.com"
SMTP_PORT=465
SMTP_USER=<EMAIL>"
SMTP_PASS="informatica1E..."
SMTP_SECURE=True
SMTP_FROM="Passisto <<EMAIL>>"

FRONTEND_NGINX = https://enterprise.passisto.com/payment/auth/login
FRONTEND_URL= https://enterprise.passisto.com

FRONTEND_NGINX_CHECKOUT_SUCESS = http://enterprise.passisto.com/payment/success
FRONTEND_NGINX_CHECKOUT_CANCEL = http://enterprise.passisto.com/payment/cancel


STRIPE_SECRET_KEY=sk_test_51PCNs92NnZaAtl1rVJGTPJSEZmGIDUBRQuuQnGTVFgiaM4aeZB110Qa3uNeqzqE8ESkSHiP0Gg3SINDeDFvm69RI00xCbjRx4I
STRIPE_WEBHOOK_SECRET=whsec_CZHLZ11dSAVEAJ8ZzYAO6sIERNa0lH88


GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"
GEMINI_API_KEY_1 = "AIzaSyA-VL6kqfGHXFba8I-JwLe70i--APcXoW4"
GEMINI_API_KEY_2= "AIzaSyBitfPXTo6LVHPxHJlvQKsz8CnN7p07Bdw"
GEMINI_API_KEY_3= "AIzaSyDiPcFIOUMJuBmW0h_t-LFOWYZFqFfPoNw"
